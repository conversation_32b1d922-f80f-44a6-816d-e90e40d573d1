package model

import (
	"time"

	"gorm.io/gorm"
)

// Admin 管理员表
type Admin struct {
	ID        uint           `gorm:"primaryKey" json:"id"`
	Username  string         `gorm:"uniqueIndex;size:50;not null" json:"username"`
	Password  string         `gorm:"size:100;not null" json:"-"` // 存储加密后的密码，不返回给前端
	Role      int            `gorm:"not null" json:"role"`       // 角色 1:超级管理员 2:普通管理员
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (Admin) TableName() string {
	return "admins"
}

// AdminRole 管理员角色常量
const (
	AdminRoleSuperAdmin = 1 // 超级管理员
	AdminRoleAdmin      = 2 // 普通管理员
)

// IsSuperAdmin 检查是否为超级管理员
func (a *Admin) IsSuperAdmin() bool {
	return a.Role == AdminRoleSuperAdmin
}

// IsAdmin 检查是否为普通管理员
func (a *Admin) IsAdmin() bool {
	return a.Role == AdminRoleAdmin
}

// GetRoleName 获取角色名称
func (a *Admin) GetRoleName() string {
	switch a.Role {
	case AdminRoleSuperAdmin:
		return "超级管理员"
	case AdminRoleAdmin:
		return "普通管理员"
	default:
		return "未知角色"
	}
}

// AdminLoginRequest 管理员登录请求
type AdminLoginRequest struct {
	Username string `json:"username" binding:"required" example:"admin"`
	Password string `json:"password" binding:"required" example:"admin123"`
}

// AdminCreateRequest 创建管理员请求
type AdminCreateRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50" example:"manager"`
	Password string `json:"password" binding:"required,min=6,max=20" example:"password123"`
	Role     int    `json:"role" binding:"required,oneof=1 2" example:"2"`
}

// AdminUpdateRequest 更新管理员请求
type AdminUpdateRequest struct {
	Password string `json:"password,omitempty" binding:"omitempty,min=6,max=20" example:"newpassword"`
	Role     int    `json:"role,omitempty" binding:"omitempty,oneof=1 2" example:"2"`
}

// AdminChangePasswordRequest 管理员修改密码请求（使用原密码）
type AdminChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required" example:"oldpassword123"`
	NewPassword string `json:"new_password" binding:"required,min=6,max=20" example:"newpassword123"`
}

// AdminResetPasswordRequest 管理员重置密码请求（使用验证码）
type AdminResetPasswordRequest struct {
	Username    string `json:"username" binding:"required" example:"admin"`
	Code        string `json:"code" binding:"required,len=6" example:"123456"`
	NewPassword string `json:"new_password" binding:"required,min=6,max=20" example:"newpassword123"`
}

// AdminForgotPasswordRequest 管理员忘记密码请求（发送验证码）
type AdminForgotPasswordRequest struct {
	Username string `json:"username" binding:"required" example:"admin"`
}

// AdminResetUserPasswordRequest 管理员重置用户密码请求
type AdminResetUserPasswordRequest struct {
	NewPassword string `json:"new_password" binding:"required,min=6,max=20" example:"newpassword123"`
}

// AdminResponse 管理员信息响应
type AdminResponse struct {
	ID        uint      `json:"id"`
	Username  string    `json:"username"`
	Role      int       `json:"role"`
	RoleName  string    `json:"role_name"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (a *Admin) ToResponse() *AdminResponse {
	return &AdminResponse{
		ID:        a.ID,
		Username:  a.Username,
		Role:      a.Role,
		RoleName:  a.GetRoleName(),
		CreatedAt: a.CreatedAt,
		UpdatedAt: a.UpdatedAt,
	}
}

// AdminListResponse 管理员列表响应
type AdminListResponse struct {
	Total int              `json:"total"`
	List  []*AdminResponse `json:"list"`
}

// AdminLoginResponse 管理员登录响应
type AdminLoginResponse struct {
	Admin *AdminResponse `json:"admin"`
	Token string         `json:"token"`
}

// HasPermission 检查是否有指定权限
func (a *Admin) HasPermission(permission string) bool {
	// 超级管理员拥有所有权限
	if a.IsSuperAdmin() {
		return true
	}

	// 普通管理员的权限检查
	switch permission {
	case "user.view", "user.manage":
		return true
	case "app.view", "app.manage":
		return true
	case "stats.view":
		return true
	case "system.config", "admin.manage", "model.config":
		return false // 只有超级管理员才有这些权限
	default:
		return false
	}
}
