package repository

import (
	"errors"
	"solve_api/internal/model"

	"gorm.io/gorm"
)

type UserRepository struct {
	db *gorm.DB
}

// NewUserRepository 创建用户仓库实例
func NewUserRepository(db *gorm.DB) *UserRepository {
	return &UserRepository{db: db}
}

// Create 创建用户
func (r *UserRepository) Create(user *model.User) error {
	return r.db.Create(user).Error
}

// GetByID 根据ID获取用户
func (r *UserRepository) GetByID(id uint) (*model.User, error) {
	var user model.User
	err := r.db.First(&user, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// GetByPhone 根据手机号获取用户
func (r *UserRepository) GetByPhone(phone string) (*model.User, error) {
	var user model.User
	err := r.db.Where("phone = ?", phone).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// Update 更新用户信息
func (r *UserRepository) Update(user *model.User) error {
	return r.db.Save(user).Error
}

// UpdateBalance 更新用户余额
func (r *UserRepository) UpdateBalance(userID uint, balance float64) error {
	return r.db.Model(&model.User{}).Where("id = ?", userID).Update("balance", balance).Error
}

// UpdateStatus 更新用户状态
func (r *UserRepository) UpdateStatus(userID uint, status int) error {
	return r.db.Model(&model.User{}).Where("id = ?", userID).Update("status", status).Error
}

// Delete 软删除用户
func (r *UserRepository) Delete(userID uint) error {
	return r.db.Delete(&model.User{}, userID).Error
}

// List 获取用户列表（分页）
func (r *UserRepository) List(offset, limit int) ([]*model.User, int64, error) {
	var users []*model.User
	var total int64

	// 获取总数
	if err := r.db.Model(&model.User{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Offset(offset).Limit(limit).Find(&users).Error
	if err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// ExistsByPhone 检查手机号是否已存在
func (r *UserRepository) ExistsByPhone(phone string) (bool, error) {
	var count int64
	err := r.db.Model(&model.User{}).Where("phone = ?", phone).Count(&count).Error
	return count > 0, err
}

// GetActiveUserCount 获取活跃用户数量
func (r *UserRepository) GetActiveUserCount() (int64, error) {
	var count int64
	err := r.db.Model(&model.User{}).Where("status = ?", model.UserStatusNormal).Count(&count).Error
	return count, err
}

// UpdatePassword 更新用户密码
func (r *UserRepository) UpdatePassword(userID uint, hashedPassword string) error {
	return r.db.Model(&model.User{}).Where("id = ?", userID).Update("password", hashedPassword).Error
}

// UpdatePasswordByPhone 根据手机号更新用户密码
func (r *UserRepository) UpdatePasswordByPhone(phone, hashedPassword string) error {
	return r.db.Model(&model.User{}).Where("phone = ?", phone).Update("password", hashedPassword).Error
}


