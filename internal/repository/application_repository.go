package repository

import (
	"errors"
	"solve_api/internal/model"

	"gorm.io/gorm"
)

type ApplicationRepository struct {
	db *gorm.DB
}

// NewApplicationRepository 创建应用仓库实例
func NewApplicationRepository(db *gorm.DB) *ApplicationRepository {
	return &ApplicationRepository{db: db}
}

// Create 创建应用
func (r *ApplicationRepository) Create(app *model.Application) error {
	return r.db.Create(app).Error
}

// GetByID 根据ID获取应用
func (r *ApplicationRepository) GetByID(id uint) (*model.Application, error) {
	var app model.Application
	err := r.db.First(&app, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &app, nil
}

// GetByAppKey 根据AppKey获取应用
func (r *ApplicationRepository) GetByAppKey(appKey string) (*model.Application, error) {
	var app model.Application
	err := r.db.Where("app_key = ?", appKey).First(&app).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &app, nil
}

// GetByUserID 根据用户ID获取应用列表
func (r *ApplicationRepository) GetByUserID(userID uint) ([]*model.Application, error) {
	var apps []*model.Application
	err := r.db.Where("user_id = ?", userID).Order("created_at DESC").Find(&apps).Error
	if err != nil {
		return nil, err
	}
	return apps, nil
}

// GetByUserIDAndID 根据用户ID和应用ID获取应用（确保用户只能访问自己的应用）
func (r *ApplicationRepository) GetByUserIDAndID(userID, appID uint) (*model.Application, error) {
	var app model.Application
	err := r.db.Where("user_id = ? AND id = ?", userID, appID).First(&app).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &app, nil
}

// Update 更新应用信息
func (r *ApplicationRepository) Update(app *model.Application) error {
	return r.db.Save(app).Error
}

// UpdateName 更新应用名称
func (r *ApplicationRepository) UpdateName(appID uint, name string) error {
	return r.db.Model(&model.Application{}).Where("id = ?", appID).Update("name", name).Error
}

// UpdateSecretKey 更新应用SecretKey
func (r *ApplicationRepository) UpdateSecretKey(appID uint, secretKey string) error {
	return r.db.Model(&model.Application{}).Where("id = ?", appID).Update("secret_key", secretKey).Error
}

// UpdateStatus 更新应用状态
func (r *ApplicationRepository) UpdateStatus(appID uint, status int) error {
	return r.db.Model(&model.Application{}).Where("id = ?", appID).Update("status", status).Error
}

// Delete 软删除应用
func (r *ApplicationRepository) Delete(appID uint) error {
	return r.db.Delete(&model.Application{}, appID).Error
}

// CountByUserID 统计用户的应用数量
func (r *ApplicationRepository) CountByUserID(userID uint) (int64, error) {
	var count int64
	err := r.db.Model(&model.Application{}).Where("user_id = ?", userID).Count(&count).Error
	return count, err
}

// ExistsByAppKey 检查AppKey是否已存在
func (r *ApplicationRepository) ExistsByAppKey(appKey string) (bool, error) {
	var count int64
	err := r.db.Model(&model.Application{}).Where("app_key = ?", appKey).Count(&count).Error
	return count > 0, err
}

// List 获取应用列表（分页，管理员用）
func (r *ApplicationRepository) List(offset, limit int) ([]*model.Application, int64, error) {
	var apps []*model.Application
	var total int64

	// 获取总数
	if err := r.db.Model(&model.Application{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Offset(offset).Limit(limit).Order("created_at DESC").Find(&apps).Error
	if err != nil {
		return nil, 0, err
	}

	return apps, total, nil
}

// GetActiveAppCount 获取活跃应用数量
func (r *ApplicationRepository) GetActiveAppCount() (int64, error) {
	var count int64
	err := r.db.Model(&model.Application{}).Where("status = ?", model.ApplicationStatusNormal).Count(&count).Error
	return count, err
}

// GetAppCountByType 根据类型统计应用数量
func (r *ApplicationRepository) GetAppCountByType(appType int) (int64, error) {
	var count int64
	err := r.db.Model(&model.Application{}).Where("type = ?", appType).Count(&count).Error
	return count, err
}
