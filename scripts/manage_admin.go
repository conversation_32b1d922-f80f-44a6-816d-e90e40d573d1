package main

import (
	"fmt"
	"log"
	"os"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/model"
	"solve_api/internal/utils"
)

// 管理员账号管理工具
func main() {
	if len(os.Args) < 2 {
		showUsage()
		return
	}

	command := os.Args[1]

	// 初始化数据库连接
	if err := initDatabase(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.CloseMySQL()

	switch command {
	case "create":
		if len(os.Args) < 5 {
			fmt.Println("用法: go run scripts/manage_admin.go create <用户名> <密码> <角色>")
			fmt.Println("角色: 1=超级管理员, 2=普通管理员")
			return
		}
		createAdmin(os.Args[2], os.Args[3], os.Args[4])
	case "update":
		if len(os.Args) < 4 {
			fmt.Println("用法: go run scripts/manage_admin.go update <用户名> <新密码>")
			return
		}
		updatePassword(os.Args[2], os.Args[3])
	case "list":
		listAdmins()
	case "delete":
		if len(os.Args) < 3 {
			fmt.Println("用法: go run scripts/manage_admin.go delete <用户名>")
			return
		}
		deleteAdmin(os.Args[2])
	case "verify":
		if len(os.Args) < 4 {
			fmt.Println("用法: go run scripts/manage_admin.go verify <用户名> <密码>")
			return
		}
		verifyAdmin(os.Args[2], os.Args[3])
	default:
		showUsage()
	}
}

// 显示使用说明
func showUsage() {
	fmt.Println("=== 管理员账号管理工具 ===")
	fmt.Println()
	fmt.Println("用法: go run scripts/manage_admin.go <命令> [参数...]")
	fmt.Println()
	fmt.Println("命令:")
	fmt.Println("  create <用户名> <密码> <角色>  - 创建管理员账号")
	fmt.Println("  update <用户名> <新密码>      - 更新管理员密码")
	fmt.Println("  list                        - 列出所有管理员")
	fmt.Println("  delete <用户名>             - 删除管理员账号")
	fmt.Println("  verify <用户名> <密码>      - 验证管理员密码")
	fmt.Println()
	fmt.Println("角色说明:")
	fmt.Println("  1 - 超级管理员 (拥有所有权限)")
	fmt.Println("  2 - 普通管理员 (有限权限)")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  go run scripts/manage_admin.go create admin888 mypassword 1")
	fmt.Println("  go run scripts/manage_admin.go update admin888 newpassword")
	fmt.Println("  go run scripts/manage_admin.go verify admin888 mypassword")
}

// 初始化数据库
func initDatabase() error {
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	if err := database.InitMySQL(&cfg.Database.MySQL); err != nil {
		return fmt.Errorf("failed to initialize MySQL: %w", err)
	}

	// 自动迁移
	db := database.GetDB()
	if err := db.AutoMigrate(&model.Admin{}); err != nil {
		return fmt.Errorf("failed to migrate admin table: %w", err)
	}

	return nil
}

// 创建管理员
func createAdmin(username, password, roleStr string) {
	fmt.Printf("=== 创建管理员账号: %s ===\n", username)

	// 验证角色
	var role int
	switch roleStr {
	case "1":
		role = model.AdminRoleSuperAdmin
		fmt.Println("角色: 超级管理员")
	case "2":
		role = model.AdminRoleAdmin
		fmt.Println("角色: 普通管理员")
	default:
		fmt.Printf("❌ 无效的角色: %s (必须是 1 或 2)\n", roleStr)
		return
	}

	db := database.GetDB()

	// 检查用户名是否已存在
	var existingAdmin model.Admin
	err := db.Where("username = ?", username).First(&existingAdmin).Error
	if err == nil {
		fmt.Printf("❌ 用户名 '%s' 已存在\n", username)
		return
	}

	// 加密密码
	hashedPassword, err := utils.HashPassword(password)
	if err != nil {
		fmt.Printf("❌ 密码加密失败: %v\n", err)
		return
	}

	// 创建管理员
	admin := model.Admin{
		Username: username,
		Password: hashedPassword,
		Role:     role,
	}

	if err := db.Create(&admin).Error; err != nil {
		fmt.Printf("❌ 创建管理员失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 管理员账号创建成功!\n")
	fmt.Printf("   ID: %d\n", admin.ID)
	fmt.Printf("   用户名: %s\n", admin.Username)
	fmt.Printf("   角色: %s\n", admin.GetRoleName())
	fmt.Printf("   创建时间: %s\n", admin.CreatedAt.Format("2006-01-02 15:04:05"))
}

// 更新密码
func updatePassword(username, newPassword string) {
	fmt.Printf("=== 更新管理员密码: %s ===\n", username)

	db := database.GetDB()

	// 查找管理员
	var admin model.Admin
	err := db.Where("username = ?", username).First(&admin).Error
	if err != nil {
		fmt.Printf("❌ 找不到管理员: %s\n", username)
		return
	}

	// 加密新密码
	hashedPassword, err := utils.HashPassword(newPassword)
	if err != nil {
		fmt.Printf("❌ 密码加密失败: %v\n", err)
		return
	}

	// 更新密码
	if err := db.Model(&admin).Update("password", hashedPassword).Error; err != nil {
		fmt.Printf("❌ 更新密码失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 密码更新成功!\n")
	fmt.Printf("   用户名: %s\n", admin.Username)
	fmt.Printf("   角色: %s\n", admin.GetRoleName())
	fmt.Printf("   更新时间: %s\n", admin.UpdatedAt.Format("2006-01-02 15:04:05"))
}

// 列出所有管理员
func listAdmins() {
	fmt.Println("=== 所有管理员账号 ===")

	db := database.GetDB()
	var admins []model.Admin

	err := db.Find(&admins).Error
	if err != nil {
		fmt.Printf("❌ 查询管理员失败: %v\n", err)
		return
	}

	if len(admins) == 0 {
		fmt.Println("📝 没有找到任何管理员账号")
		return
	}

	fmt.Printf("📋 共找到 %d 个管理员账号:\n\n", len(admins))
	for i, admin := range admins {
		fmt.Printf("%d. 用户名: %s\n", i+1, admin.Username)
		fmt.Printf("   ID: %d\n", admin.ID)
		fmt.Printf("   角色: %s\n", admin.GetRoleName())
		fmt.Printf("   创建时间: %s\n", admin.CreatedAt.Format("2006-01-02 15:04:05"))
		fmt.Printf("   更新时间: %s\n", admin.UpdatedAt.Format("2006-01-02 15:04:05"))
		fmt.Println()
	}
}

// 删除管理员
func deleteAdmin(username string) {
	fmt.Printf("=== 删除管理员账号: %s ===\n", username)

	db := database.GetDB()

	// 查找管理员
	var admin model.Admin
	err := db.Where("username = ?", username).First(&admin).Error
	if err != nil {
		fmt.Printf("❌ 找不到管理员: %s\n", username)
		return
	}

	// 软删除管理员
	if err := db.Delete(&admin).Error; err != nil {
		fmt.Printf("❌ 删除管理员失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 管理员账号删除成功!\n")
	fmt.Printf("   用户名: %s\n", admin.Username)
	fmt.Printf("   角色: %s\n", admin.GetRoleName())
}

// 验证管理员密码
func verifyAdmin(username, password string) {
	fmt.Printf("=== 验证管理员密码: %s ===\n", username)

	db := database.GetDB()

	// 查找管理员
	var admin model.Admin
	err := db.Where("username = ?", username).First(&admin).Error
	if err != nil {
		fmt.Printf("❌ 找不到管理员: %s\n", username)
		return
	}

	// 验证密码
	if utils.CheckPassword(password, admin.Password) {
		fmt.Printf("✅ 密码验证成功!\n")
		fmt.Printf("   用户名: %s\n", admin.Username)
		fmt.Printf("   角色: %s\n", admin.GetRoleName())
		fmt.Printf("   最后更新: %s\n", admin.UpdatedAt.Format("2006-01-02 15:04:05"))
	} else {
		fmt.Printf("❌ 密码验证失败!\n")
	}
}
