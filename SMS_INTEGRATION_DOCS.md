# 阿里云短信服务接入文档

## 概述

本文档描述了如何在拍照搜题API服务中集成阿里云短信服务，实现真实的验证码发送功能。

## 阿里云短信服务配置

### 1. 服务信息

- **服务商**: 阿里云短信服务
- **产品**: 短信服务（SMS）
- **API版本**: 2017-05-25

### 2. 账户信息

| 配置项 | 值 | 说明 |
|--------|-----|------|
| AccessKey ID | `LTAI5t9g26wqutn692bcCRmR` | 阿里云访问密钥ID |
| AccessKey Secret | `******************************` | 阿里云访问密钥Secret |
| 短信签名 | `媒兔记账` | 短信发送方签名 |
| 模板代码 | `SMS_294081777` | 验证码短信模板 |
| 模板变量 | `${code}` | 验证码变量 |
| 服务区域 | `cn-hangzhou` | 阿里云服务区域 |

### 3. 短信模板

**模板代码**: `SMS_294081777`

**模板内容**: 您的验证码为：${code}，该验证码5分钟内有效，请勿泄漏于他人！

**变量说明**:
- `${code}`: 6位数字验证码

## 技术实现

### 1. 依赖包

项目使用以下阿里云SDK包：

```go
require (
    github.com/alibabacloud-go/darabonba-openapi/v2 v2.0.4
    github.com/alibabacloud-go/dysmsapi-20170525/v3 v3.0.6
    github.com/alibabacloud-go/tea v1.2.1
)
```

### 2. 配置结构

```yaml
sms:
  provider: aliyun
  access_key_id: "LTAI5t9g26wqutn692bcCRmR"
  access_key_secret: "******************************"
  sign_name: "青岛果沐云"
  template_code: "SMS_294081777"
  region: "cn-hangzhou"
```

### 3. 服务接口

```go
type SMSService interface {
    SendCode(phone, code string) error
}
```

### 4. 实现类

- **AliyunSMSService**: 阿里云短信服务实现
- **MockSMSService**: 模拟短信服务（开发测试用）

## 配置方式

### 1. 配置文件配置

在 `config/config.yaml` 中配置：

```yaml
sms:
  provider: aliyun
  access_key_id: "your_access_key_id"
  access_key_secret: "your_access_key_secret"
  sign_name: "your_sign_name"
  template_code: "your_template_code"
  region: "cn-hangzhou"
```

### 2. 环境变量配置

```bash
export SOLVE_API_SMS_PROVIDER=aliyun
export SOLVE_API_SMS_ACCESS_KEY_ID=LTAI5t9g26wqutn692bcCRmR
export SOLVE_API_SMS_ACCESS_KEY_SECRET=******************************
export SOLVE_API_SMS_SIGN_NAME=青岛果沐云
export SOLVE_API_SMS_TEMPLATE_CODE=SMS_294081777
```

### 3. 生产环境配置

使用 `config/config.prod.yaml` 配置文件，或通过环境变量覆盖。

## 使用方法

### 1. 发送验证码API

**接口**: `POST /api/v1/user/send-code`

**请求参数**:
```json
{
  "phone": "13800138000"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": null
}
```

### 2. 验证码使用

发送的验证码用于用户注册：

**接口**: `POST /api/v1/user/register`

**请求参数**:
```json
{
  "phone": "13800138000",
  "password": "123456",
  "code": "123456",
  "invite_code": "SOLVE2024"
}
```

## 错误处理

### 1. 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| InvalidAccessKeyId.NotFound | AccessKey不存在 | 检查AccessKey ID是否正确 |
| SignatureDoesNotMatch | 签名不匹配 | 检查AccessKey Secret是否正确 |
| InvalidTemplate.Malformed | 模板格式错误 | 检查模板代码是否正确 |
| InvalidSignName.Malformed | 签名格式错误 | 检查短信签名是否正确 |
| Throttling.User | 用户级别限流 | 降低发送频率 |
| isv.MOBILE_NUMBER_ILLEGAL | 手机号格式错误 | 检查手机号格式 |

### 2. 错误响应示例

```json
{
  "code": 500,
  "message": "sms send failed: InvalidTemplate.Malformed"
}
```

## 限制说明

### 1. 发送频率限制

- **单个手机号**: 60秒内只能发送1条验证码
- **单个IP**: 每小时最多发送100条
- **单个签名**: 每天最多发送1000条

### 2. 验证码规则

- **长度**: 6位数字
- **有效期**: 5分钟
- **存储**: Redis缓存
- **重试**: 验证失败不限制重试次数

## 安全建议

### 1. 密钥安全

- 不要将AccessKey信息提交到代码仓库
- 使用环境变量或密钥管理服务
- 定期轮换AccessKey

### 2. 防刷机制

- 实现IP级别的发送频率限制
- 添加图形验证码防止机器人
- 监控异常发送行为

### 3. 日志记录

- 记录所有短信发送请求
- 记录发送结果和错误信息
- 定期分析发送统计数据

## 测试方法

### 1. 单元测试

```bash
go test -v ./internal/utils -run TestSMSService
```

### 2. 集成测试

```bash
# 使用真实配置测试
./test_api.sh
```

### 3. 手动测试

```bash
curl -X POST http://localhost:8080/api/v1/user/send-code \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000"}'
```

## 监控和运维

### 1. 监控指标

- 短信发送成功率
- 短信发送延迟
- 错误码分布
- 发送量统计

### 2. 告警规则

- 发送成功率低于95%
- 单小时发送量异常
- 连续发送失败

### 3. 日志分析

```bash
# 查看短信发送日志
grep "SMS" logs/app.log

# 统计发送成功率
grep "SMS.*success" logs/app.log | wc -l
```

## 成本优化

### 1. 费用说明

- **国内短信**: 约0.045元/条
- **国际短信**: 约0.7元/条
- **月租费**: 无

### 2. 优化建议

- 合理设置发送频率限制
- 使用模拟服务进行开发测试
- 监控异常发送行为
- 定期清理无效手机号

## 故障排查

### 1. 常见问题

**问题**: 验证码发送失败
**排查步骤**:
1. 检查网络连接
2. 验证AccessKey配置
3. 检查手机号格式
4. 查看错误日志

**问题**: 验证码收不到
**排查步骤**:
1. 检查手机号是否正确
2. 确认短信签名是否审核通过
3. 检查模板是否正确
4. 查看发送状态

### 2. 调试模式

开发环境可以使用模拟模式：

```yaml
sms:
  provider: mock
```

这样验证码会在控制台输出，不会真实发送短信。

## 总结

阿里云短信服务已成功集成到拍照搜题API服务中，支持：

- ✅ 真实短信发送
- ✅ 验证码生成和验证
- ✅ 错误处理和重试
- ✅ 频率限制和安全防护
- ✅ 配置灵活切换（真实/模拟）
- ✅ 完整的日志记录

服务已准备好用于生产环境，建议在部署前进行充分测试。
