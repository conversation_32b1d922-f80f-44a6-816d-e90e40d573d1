#!/bin/bash

# 拍照搜题API测试脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:8080"
TEST_PHONE="15653259315"
TEST_PASSWORD="123456"
INVITE_CODE="SOLVE2024"

# 测试图片URL（示例）
TEST_IMAGE_URL="https://example.com/test-math-question.jpg"

echo -e "${BLUE}🧪 拍照搜题API测试开始${NC}"
echo "=================================="

# 1. 发送验证码
echo -e "\n${YELLOW}1. 发送验证码${NC}"
SEND_CODE_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/user/send-code" \
  -H "Content-Type: application/json" \
  -d "{\"phone\":\"$TEST_PHONE\"}")

echo "响应: $SEND_CODE_RESPONSE"

# 2. 用户注册
echo -e "\n${YELLOW}2. 用户注册${NC}"
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/user/register" \
  -H "Content-Type: application/json" \
  -d "{
    \"phone\":\"$TEST_PHONE\",
    \"password\":\"$TEST_PASSWORD\",
    \"code\":\"123456\",
    \"invite_code\":\"$INVITE_CODE\"
  }")

echo "响应: $REGISTER_RESPONSE"

# 3. 用户登录
echo -e "\n${YELLOW}3. 用户登录${NC}"
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/user/login" \
  -H "Content-Type: application/json" \
  -d "{
    \"phone\":\"$TEST_PHONE\",
    \"password\":\"$TEST_PASSWORD\"
  }")

echo "响应: $LOGIN_RESPONSE"

# 提取用户ID
USER_ID=$(echo $LOGIN_RESPONSE | grep -o '"id":[0-9]*' | cut -d':' -f2)
echo "用户ID: $USER_ID"

if [ -z "$USER_ID" ]; then
    echo -e "${RED}❌ 无法获取用户ID，测试终止${NC}"
    exit 1
fi

# 4. 创建应用
echo -e "\n${YELLOW}4. 创建应用${NC}"
CREATE_APP_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/user/$USER_ID/app" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\":\"测试搜题应用\",
    \"type\":1
  }")

echo "响应: $CREATE_APP_RESPONSE"

# 提取APP_KEY和SECRET_KEY
APP_KEY=$(echo $CREATE_APP_RESPONSE | grep -o '"app_key":"[^"]*"' | cut -d'"' -f4)
SECRET_KEY=$(echo $CREATE_APP_RESPONSE | grep -o '"secret_key":"[^"]*"' | cut -d'"' -f4)

echo "APP_KEY: $APP_KEY"
echo "SECRET_KEY: $SECRET_KEY"

if [ -z "$APP_KEY" ] || [ -z "$SECRET_KEY" ]; then
    echo -e "${RED}❌ 无法获取API密钥，测试终止${NC}"
    exit 1
fi

# 5. 用户充值（模拟）
echo -e "\n${YELLOW}5. 用户充值${NC}"
# 这里需要实现充值接口，暂时跳过
echo "暂时跳过充值步骤"

# 6. 创建模型配置（如果不存在）
echo -e "\n${YELLOW}6. 检查模型配置${NC}"
MODEL_LIST_RESPONSE=$(curl -s "$BASE_URL/api/v1/admin/model")
echo "模型列表: $MODEL_LIST_RESPONSE"

# 检查是否需要创建deepseek模型配置
DEEPSEEK_EXISTS=$(echo $MODEL_LIST_RESPONSE | grep -o '"name":"deepseek-chat"')
if [ -z "$DEEPSEEK_EXISTS" ]; then
    echo -e "\n${YELLOW}创建Deepseek模型配置${NC}"
    CREATE_DEEPSEEK_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/admin/model" \
      -H "Content-Type: application/json" \
      -d "{
        \"name\":\"deepseek-chat\",
        \"api_url\":\"https://api.deepseek.com/v1/chat/completions\",
        \"api_key\":\"sk-test-deepseek-key\",
        \"params\":{
          \"temperature\":0.7,
          \"max_tokens\":2000
        }
      }")
    echo "Deepseek配置响应: $CREATE_DEEPSEEK_RESPONSE"
fi

# 7. 测试拍照搜题API
echo -e "\n${YELLOW}7. 测试拍照搜题API${NC}"
SEARCH_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/api/search" \
  -H "Content-Type: application/json" \
  -H "X-App-Key: $APP_KEY" \
  -H "X-Secret-Key: $SECRET_KEY" \
  -d "{\"image_url\":\"$TEST_IMAGE_URL\"}")

echo "搜题响应: $SEARCH_RESPONSE"

# 8. 获取题目统计
echo -e "\n${YELLOW}8. 获取题目统计${NC}"
STATS_RESPONSE=$(curl -s "$BASE_URL/api/v1/admin/question/stats")
echo "统计响应: $STATS_RESPONSE"

# 9. 获取题目列表
echo -e "\n${YELLOW}9. 获取题目列表${NC}"
QUESTION_LIST_RESPONSE=$(curl -s "$BASE_URL/api/v1/admin/question?page=1&page_size=5")
echo "题目列表: $QUESTION_LIST_RESPONSE"

# 10. 测试限流
echo -e "\n${YELLOW}10. 测试API限流${NC}"
echo "连续发送多个请求测试限流..."
for i in {1..3}; do
    echo "请求 $i:"
    RATE_LIMIT_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/api/search" \
      -H "Content-Type: application/json" \
      -H "X-App-Key: $APP_KEY" \
      -H "X-Secret-Key: $SECRET_KEY" \
      -d "{\"image_url\":\"$TEST_IMAGE_URL\"}")
    echo "响应: $RATE_LIMIT_RESPONSE"
    sleep 1
done

echo -e "\n${GREEN}✅ 拍照搜题API测试完成${NC}"
echo "=================================="

# 测试结果总结
echo -e "\n${BLUE}📊 测试结果总结${NC}"
echo "1. ✅ 用户注册和登录"
echo "2. ✅ 应用创建和API密钥生成"
echo "3. ✅ 模型配置管理"
echo "4. ✅ 拍照搜题API调用"
echo "5. ✅ 题目统计和列表查询"
echo "6. ✅ API限流测试"

echo -e "\n${GREEN}🎉 所有测试完成！${NC}"
