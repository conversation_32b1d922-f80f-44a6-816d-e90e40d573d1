package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"solve_api/internal/api"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/middleware"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"solve_api/internal/service"
	"solve_api/internal/utils"
	"syscall"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

func main() {
	// 1. 加载配置
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 2. 初始化日志
	logger := initLogger(cfg)
	defer logger.Sync()

	// 3. 初始化数据库
	if err := database.InitMySQL(&cfg.Database.MySQL); err != nil {
		logger.Fatal("Failed to initialize MySQL", zap.Error(err))
	}
	defer database.CloseMySQL()

	// 4. 初始化Redis（允许失败）
	if err := database.InitRedis(&cfg.Redis); err != nil {
		logger.Warn("Failed to initialize Redis, SMS features will be disabled", zap.Error(err))
	} else {
		defer database.CloseRedis()
		logger.Info("Redis connected successfully")
	}

	// 5. 自动迁移数据库表
	if err := autoMigrate(); err != nil {
		logger.Fatal("Failed to migrate database", zap.Error(err))
	}

	// 6. 初始化系统配置
	if err := initSystemConfig(); err != nil {
		logger.Fatal("Failed to initialize system config", zap.Error(err))
	}

	// 6. 初始化短信服务
	smsService, err := utils.NewSMSService(&cfg.SMS)
	if err != nil {
		logger.Fatal("Failed to initialize SMS service", zap.Error(err))
	}

	// 7. 初始化依赖注入
	userRepo := repository.NewUserRepository(database.GetDB())
	configRepo := repository.NewConfigRepository(database.GetDB())
	appRepo := repository.NewApplicationRepository(database.GetDB())
	adminRepo := repository.NewAdminRepository(database.GetDB())

	userService := service.NewUserService(userRepo, configRepo, smsService)
	appService := service.NewApplicationService(appRepo, userRepo)
	adminService := service.NewAdminService(adminRepo, userRepo, database.GetRedis(), smsService)

	userHandler := api.NewUserHandler(userService)
	appHandler := api.NewApplicationHandler(appService)
	adminHandler := api.NewAdminHandler(adminService)

	// 8. 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 9. 创建路由
	router := setupRouter(logger, userHandler, appHandler, adminHandler)

	// 10. 启动服务器
	addr := fmt.Sprintf(":%d", cfg.Server.Port)
	logger.Info("Starting server", zap.String("addr", addr))

	// 优雅关闭
	go func() {
		if err := router.Run(addr); err != nil {
			logger.Fatal("Failed to start server", zap.Error(err))
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Server shutting down...")
}

// initLogger 初始化日志
func initLogger(cfg *config.Config) *zap.Logger {
	// 配置日志轮转
	writer := &lumberjack.Logger{
		Filename:   cfg.Log.Filename,
		MaxSize:    cfg.Log.MaxSize,
		MaxAge:     cfg.Log.MaxAge,
		MaxBackups: cfg.Log.MaxBackups,
		Compress:   cfg.Log.Compress,
	}

	// 配置日志级别
	var level zapcore.Level
	switch cfg.Log.Level {
	case "debug":
		level = zapcore.DebugLevel
	case "info":
		level = zapcore.InfoLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	default:
		level = zapcore.InfoLevel
	}

	// 创建编码器配置
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.TimeKey = "timestamp"
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder

	// 创建核心
	core := zapcore.NewCore(
		zapcore.NewJSONEncoder(encoderConfig),
		zapcore.AddSync(writer),
		level,
	)

	// 创建logger
	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))
	return logger
}

// setupRouter 设置路由
func setupRouter(logger *zap.Logger, userHandler *api.UserHandler, appHandler *api.ApplicationHandler, adminHandler *api.AdminHandler) *gin.Engine {
	router := gin.New()

	// 添加中间件
	router.Use(middleware.CORS())
	router.Use(middleware.Logger(logger))
	router.Use(middleware.Recovery(logger))

	// API路由组
	v1 := router.Group("/api/v1")
	{
		// 用户相关路由
		userGroup := v1.Group("/user")
		{
			userGroup.POST("/register", userHandler.Register)
			userGroup.POST("/login", userHandler.Login)
			userGroup.GET("/profile/:user_id", userHandler.GetProfile)
			userGroup.PUT("/profile/:user_id", userHandler.UpdateProfile)
			userGroup.POST("/send-code", userHandler.SendCode)

			// 用户密码管理
			userGroup.PUT("/:user_id/change-password", userHandler.ChangePassword)
			userGroup.POST("/forgot-password", userHandler.SendForgotPasswordCode)
			userGroup.POST("/reset-password", userHandler.ResetPassword)
		}

		// 应用管理路由
		appGroup := v1.Group("/user/:user_id/app")
		{
			appGroup.POST("", appHandler.Create)
			appGroup.GET("", appHandler.GetList)
			appGroup.GET("/:app_id", appHandler.GetByID)
			appGroup.PUT("/:app_id", appHandler.Update)
			appGroup.PUT("/:app_id/reset-secret", appHandler.ResetSecretKey)
			appGroup.PUT("/:app_id/status", appHandler.UpdateStatus)
		}

		// 管理员相关路由
		adminGroup := v1.Group("/admin")
		{
			adminGroup.POST("/login", adminHandler.Login)
			adminGroup.POST("/forgot-password", adminHandler.SendForgotPasswordCode)
			adminGroup.POST("/reset-password", adminHandler.ResetPassword)

			// 管理员密码管理
			adminGroup.PUT("/:admin_id/change-password", adminHandler.ChangePassword)

			// 管理员操作用户
			adminGroup.PUT("/:admin_id/user/:user_id/reset-password", adminHandler.ResetUserPassword)
		}
	}

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"app":    config.GlobalConfig.App.Name,
			"version": config.GlobalConfig.App.Version,
		})
	})

	return router
}

// autoMigrate 自动迁移数据库表
func autoMigrate() error {
	db := database.GetDB()
	return db.AutoMigrate(
		&model.User{},
		&model.SystemConfig{},
		&model.Admin{},
		&model.Application{},
	)
}

// initSystemConfig 初始化系统配置
func initSystemConfig() error {
	configRepo := repository.NewConfigRepository(database.GetDB())

	// 初始化默认邀请码
	if err := configRepo.SetValue(
		model.ConfigKeyInviteCode,
		config.GlobalConfig.App.InviteCode,
		"系统邀请码",
	); err != nil {
		return fmt.Errorf("failed to set invite code: %w", err)
	}

	// 初始化默认限流配置
	if err := configRepo.SetValue(
		model.ConfigKeyRateLimit,
		fmt.Sprintf("%d", config.GlobalConfig.App.RateLimit),
		"API限流配置（次/秒）",
	); err != nil {
		return fmt.Errorf("failed to set rate limit: %w", err)
	}

	// 初始化默认缓存TTL
	if err := configRepo.SetValue(
		model.ConfigKeyCacheTTL,
		fmt.Sprintf("%d", config.GlobalConfig.App.CacheTTL),
		"缓存TTL配置（秒）",
	); err != nil {
		return fmt.Errorf("failed to set cache TTL: %w", err)
	}

	return nil
}
