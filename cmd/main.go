package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"solve_api/internal/api"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/middleware"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"solve_api/internal/service"
	"solve_api/internal/utils"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

func main() {
	// 1. 加载配置
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 2. 初始化日志
	logger := initLogger(cfg)
	defer logger.Sync()

	// 3. 初始化数据库
	if err := database.InitMySQL(&cfg.Database.MySQL); err != nil {
		logger.Fatal("Failed to initialize MySQL", zap.Error(err))
	}
	defer database.CloseMySQL()

	// 4. 初始化Redis（允许失败）
	if err := database.InitRedis(&cfg.Redis); err != nil {
		logger.Warn("Failed to initialize Redis, SMS features will be disabled", zap.Error(err))
	} else {
		defer database.CloseRedis()
		logger.Info("Redis connected successfully")
	}

	// 5. 自动迁移数据库表
	if err := autoMigrate(); err != nil {
		logger.Fatal("Failed to migrate database", zap.Error(err))
	}

	// 6. 初始化系统配置
	if err := initSystemConfig(); err != nil {
		logger.Fatal("Failed to initialize system config", zap.Error(err))
	}

	// 6. 初始化短信服务
	smsService, err := utils.NewSMSService(&cfg.SMS)
	if err != nil {
		logger.Fatal("Failed to initialize SMS service", zap.Error(err))
	}

	// 7. 初始化依赖注入
	userRepo := repository.NewUserRepository(database.GetDB())
	configRepo := repository.NewConfigRepository(database.GetDB())
	appRepo := repository.NewApplicationRepository(database.GetDB())
	adminRepo := repository.NewAdminRepository(database.GetDB())
	modelConfigRepo := repository.NewModelConfigRepository(database.GetDB())
	priceConfigRepo := repository.NewPriceConfigRepository(database.GetDB())
	questionRepo := repository.NewQuestionRepository(database.GetDB())
	questionCacheRepo := repository.NewQuestionCacheRepository(database.GetRedis())
	balanceLogRepo := repository.NewBalanceLogRepository(database.GetDB())
	apiLogRepo := repository.NewAPILogRepository(database.GetDB())
	statsRepo := repository.NewStatsRepository(database.GetDB())

	userService := service.NewUserService(userRepo, configRepo, smsService)
	appService := service.NewApplicationService(appRepo, userRepo)
	adminService := service.NewAdminService(adminRepo, userRepo, database.GetRedis(), smsService)
	modelConfigService := service.NewModelConfigService(modelConfigRepo)
	priceService := service.NewPriceService(priceConfigRepo)
	balanceService := service.NewBalanceService(userRepo, balanceLogRepo, priceConfigRepo)
	aiService := service.NewAIService(modelConfigRepo)
	questionService := service.NewQuestionService(questionRepo, questionCacheRepo, aiService, userRepo, balanceLogRepo)
	apiLogService := service.NewAPILogService(apiLogRepo)
	statsService := service.NewStatsService(statsRepo, apiLogRepo, userRepo, appRepo, questionRepo)
	configService := service.NewConfigService(configRepo)

	userHandler := api.NewUserHandler(userService)
	appHandler := api.NewApplicationHandler(appService)
	adminHandler := api.NewAdminHandler(adminService)
	modelConfigHandler := api.NewModelConfigHandler(modelConfigService)
	priceHandler := api.NewPriceHandler(priceService)
	balanceHandler := api.NewBalanceHandler(balanceService)
	questionHandler := api.NewQuestionHandler(questionService)
	statsHandler := api.NewStatsHandler(statsService, apiLogService)
	systemHandler := api.NewSystemHandler(configService, statsService, apiLogService)

	// 8. 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 9. 创建路由
	router := setupRouter(logger, userHandler, appHandler, adminHandler, modelConfigHandler, priceHandler, balanceHandler, questionHandler, statsHandler, systemHandler)

	// 10. 启动服务器
	addr := fmt.Sprintf(":%d", cfg.Server.Port)
	logger.Info("Starting server", zap.String("addr", addr))

	// 优雅关闭
	go func() {
		if err := router.Run(addr); err != nil {
			logger.Fatal("Failed to start server", zap.Error(err))
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Server shutting down...")
}

// initLogger 初始化日志
func initLogger(cfg *config.Config) *zap.Logger {
	// 配置日志轮转
	writer := &lumberjack.Logger{
		Filename:   cfg.Log.Filename,
		MaxSize:    cfg.Log.MaxSize,
		MaxAge:     cfg.Log.MaxAge,
		MaxBackups: cfg.Log.MaxBackups,
		Compress:   cfg.Log.Compress,
	}

	// 配置日志级别
	var level zapcore.Level
	switch cfg.Log.Level {
	case "debug":
		level = zapcore.DebugLevel
	case "info":
		level = zapcore.InfoLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	default:
		level = zapcore.InfoLevel
	}

	// 创建编码器配置
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.TimeKey = "timestamp"
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder

	// 创建核心
	core := zapcore.NewCore(
		zapcore.NewJSONEncoder(encoderConfig),
		zapcore.AddSync(writer),
		level,
	)

	// 创建logger
	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))
	return logger
}

// setupRouter 设置路由
func setupRouter(logger *zap.Logger, userHandler *api.UserHandler, appHandler *api.ApplicationHandler, adminHandler *api.AdminHandler, modelConfigHandler *api.ModelConfigHandler, priceHandler *api.PriceHandler, balanceHandler *api.BalanceHandler, questionHandler *api.QuestionHandler, statsHandler *api.StatsHandler, systemHandler *api.SystemHandler) *gin.Engine {
	router := gin.New()

	// 添加中间件
	router.Use(middleware.CORS())
	router.Use(middleware.Logger(logger))
	router.Use(middleware.Recovery(logger))
	router.Use(middleware.APILogger()) // API调用日志记录

	// 添加限流中间件（针对用户接口）
	router.Use(middleware.RateLimitByIP(100, time.Minute)) // 每分钟100次请求

	// API路由组
	v1 := router.Group("/api/v1")
	{
		// 用户相关路由
		userGroup := v1.Group("/user")
		{
			userGroup.POST("/register", userHandler.Register)
			userGroup.POST("/login", userHandler.Login)
			userGroup.GET("/profile/:user_id", userHandler.GetProfile)
			userGroup.PUT("/profile/:user_id", userHandler.UpdateProfile)
			userGroup.POST("/send-code", userHandler.SendCode)

			// 用户密码管理
			userGroup.PUT("/:user_id/change-password", userHandler.ChangePassword)
			userGroup.POST("/forgot-password", userHandler.SendForgotPasswordCode)
			userGroup.POST("/reset-password", userHandler.ResetPassword)

			// 用户余额管理
			userGroup.POST("/:user_id/recharge", balanceHandler.Recharge)
			userGroup.GET("/:user_id/balance", balanceHandler.GetBalance)
			userGroup.GET("/:user_id/balance/logs", balanceHandler.GetBalanceLogs)
			userGroup.GET("/:user_id/balance/stats", balanceHandler.GetBalanceStats)
			userGroup.GET("/:user_id/balance/check", balanceHandler.CheckBalance)
		}

		// 应用管理路由
		appGroup := v1.Group("/user/:user_id/app")
		{
			appGroup.POST("", appHandler.Create)
			appGroup.GET("", appHandler.GetList)
			appGroup.GET("/:app_id", appHandler.GetByID)
			appGroup.PUT("/:app_id", appHandler.Update)
			appGroup.PUT("/:app_id/reset-secret", appHandler.ResetSecretKey)
			appGroup.PUT("/:app_id/status", appHandler.UpdateStatus)
		}

		// 管理员相关路由
		adminGroup := v1.Group("/admin")
		{
			adminGroup.POST("/login", adminHandler.Login)
			adminGroup.POST("/forgot-password", adminHandler.SendForgotPasswordCode)
			adminGroup.POST("/reset-password", adminHandler.ResetPassword)

			// 管理员密码管理
			adminGroup.PUT("/:admin_id/change-password", adminHandler.ChangePassword)

			// 管理员操作用户
			adminGroup.PUT("/:admin_id/user/:user_id/reset-password", adminHandler.ResetUserPassword)

			// 模型配置管理
			modelGroup := adminGroup.Group("/model")
			{
				modelGroup.POST("", modelConfigHandler.Create)
				modelGroup.GET("", modelConfigHandler.GetList)
				modelGroup.GET("/enabled", modelConfigHandler.GetEnabled)
				modelGroup.GET("/:id", modelConfigHandler.GetByID)
				modelGroup.PUT("/:id", modelConfigHandler.Update)
				modelGroup.PUT("/:id/status", modelConfigHandler.UpdateStatus)
				modelGroup.DELETE("/:id", modelConfigHandler.Delete)
			}

			// 题目管理
			questionGroup := adminGroup.Group("/question")
			{
				questionGroup.GET("", questionHandler.GetList)
				questionGroup.GET("/stats", questionHandler.GetStats)
				questionGroup.GET("/search", questionHandler.SearchQuestions)
				questionGroup.GET("/subject/:subject", questionHandler.GetBySubject)
				questionGroup.GET("/:id", questionHandler.GetByID)
				questionGroup.POST("/cache/clear", questionHandler.ClearCache)
			}

			// 价格配置管理
			priceGroup := adminGroup.Group("/price")
			{
				priceGroup.POST("", priceHandler.CreatePriceConfig)
				priceGroup.GET("", priceHandler.GetPriceConfigList)
				priceGroup.GET("/:id", priceHandler.GetPriceConfig)
				priceGroup.PUT("/:id", priceHandler.UpdatePriceConfig)
				priceGroup.DELETE("/:id", priceHandler.DeletePriceConfig)
				priceGroup.GET("/service/:service_type", priceHandler.GetPriceByService)
				priceGroup.PUT("/service/:service_type/default", priceHandler.SetDefaultPrice)
				priceGroup.PUT("/service/:service_type/user/:user_id", priceHandler.SetUserPrice)
			}

			// 用户余额管理（管理员）
			adminGroup.POST("/user/:user_id/refund", balanceHandler.Refund)

			// 统计分析
			statsGroup := adminGroup.Group("/stats")
			{
				statsGroup.GET("/system", statsHandler.GetSystemStats)
				statsGroup.GET("/system/range", statsHandler.GetSystemStatsRange)
				statsGroup.GET("/system/total", statsHandler.GetSystemStatsTotal)
				statsGroup.GET("/user/:user_id", statsHandler.GetUserStats)
				statsGroup.GET("/user/:user_id/range", statsHandler.GetUserStatsRange)
				statsGroup.GET("/user/:user_id/total", statsHandler.GetUserStatsTotal)
				statsGroup.GET("/top-users", statsHandler.GetTopUsers)
				statsGroup.GET("/recent", statsHandler.GetRecentStats)
				statsGroup.POST("/generate", statsHandler.GenerateDailyStats)
			}

			// API日志管理
			logGroup := adminGroup.Group("/logs")
			{
				logGroup.GET("/api", statsHandler.GetAPILogs)
				logGroup.GET("/api/user/:user_id", statsHandler.GetUserAPILogs)
				logGroup.GET("/api/range", statsHandler.GetAPILogsByDateRange)
			}

			// 系统管理
			systemGroup := adminGroup.Group("/system")
			{
				systemGroup.GET("/info", systemHandler.GetSystemInfo)
				systemGroup.GET("/health", systemHandler.GetSystemHealth)
				systemGroup.GET("/dashboard", systemHandler.GetDashboard)
				systemGroup.GET("/metrics", systemHandler.GetSystemMetrics)
				systemGroup.GET("/logs", systemHandler.GetSystemLogs)
				systemGroup.POST("/cleanup", systemHandler.CleanupSystem)
				systemGroup.POST("/backup", systemHandler.BackupSystem)
				systemGroup.POST("/restore", systemHandler.RestoreSystem)
				systemGroup.PUT("/config", systemHandler.UpdateSystemConfig)
			}
		}

		// 业务API路由（需要API密钥认证）
		apiGroup := v1.Group("/api")
		apiGroup.Use(middleware.APIAuth())
		apiGroup.Use(middleware.RateLimit())
		apiGroup.Use(middleware.BalanceCheck())
		apiGroup.Use(middleware.ParamValidator())
		{
			// 拍照搜题接口
			apiGroup.POST("/search", questionHandler.Search)
		}
	}

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"app":    config.GlobalConfig.App.Name,
			"version": config.GlobalConfig.App.Version,
		})
	})

	return router
}

// autoMigrate 自动迁移数据库表
func autoMigrate() error {
	db := database.GetDB()
	return db.AutoMigrate(
		&model.User{},
		&model.SystemConfig{},
		&model.Admin{},
		&model.Application{},
		&model.ModelConfig{},
		&model.PriceConfig{},
		&model.BalanceLog{},
		&model.Question{},
		&model.APILog{},
		&model.SystemStats{},
		&model.UserStats{},
	)
}

// initSystemConfig 初始化系统配置
func initSystemConfig() error {
	configRepo := repository.NewConfigRepository(database.GetDB())

	// 初始化默认邀请码
	if err := configRepo.SetValue(
		model.ConfigKeyInviteCode,
		config.GlobalConfig.App.InviteCode,
		"系统邀请码",
	); err != nil {
		return fmt.Errorf("failed to set invite code: %w", err)
	}

	// 初始化默认限流配置
	if err := configRepo.SetValue(
		model.ConfigKeyRateLimit,
		fmt.Sprintf("%d", config.GlobalConfig.App.RateLimit),
		"API限流配置（次/秒）",
	); err != nil {
		return fmt.Errorf("failed to set rate limit: %w", err)
	}

	// 初始化默认缓存TTL
	if err := configRepo.SetValue(
		model.ConfigKeyCacheTTL,
		fmt.Sprintf("%d", config.GlobalConfig.App.CacheTTL),
		"缓存TTL配置（秒）",
	); err != nil {
		return fmt.Errorf("failed to set cache TTL: %w", err)
	}

	return nil
}
