# 开发测试指南

## 快速开始

### 🚀 一键启动测试

```bash
# 最快速的启动方式
./quick.sh
```

这个命令会：
1. 自动停止可能运行的服务
2. 启动新的服务实例
3. 检查服务健康状态
4. 显示常用命令提示

### 📋 开发脚本命令

使用 `./dev.sh` 脚本进行开发测试：

```bash
# 查看所有可用命令
./dev.sh help
```

#### 服务管理命令

| 命令 | 功能 | 说明 |
|------|------|------|
| `./dev.sh start` | 启动服务 | 后台启动，生成PID文件 |
| `./dev.sh stop` | 停止服务 | 优雅停止服务 |
| `./dev.sh restart` | 重启服务 | 先停止再启动 |
| `./dev.sh status` | 查看状态 | 显示运行状态和资源使用 |

#### 开发测试命令

| 命令 | 功能 | 说明 |
|------|------|------|
| `./dev.sh dev` | 开发模式 | 前台运行，实时日志输出 |
| `./dev.sh test` | 运行测试 | 执行API自动化测试 |
| `./dev.sh logs` | 查看日志 | 实时日志输出 |
| `./dev.sh health` | 健康检查 | 检查服务是否正常 |

#### 构建和维护命令

| 命令 | 功能 | 说明 |
|------|------|------|
| `./dev.sh build` | 构建应用 | 编译生成可执行文件 |
| `./dev.sh deps` | 安装依赖 | 下载Go模块依赖 |
| `./dev.sh clean` | 清理文件 | 清理日志和临时文件 |

### 🛠 Makefile快捷命令

也可以使用make命令：

```bash
# 开发服务管理
make dev-start     # 启动服务
make dev-stop      # 停止服务
make dev-restart   # 重启服务
make dev-status    # 查看状态
make dev-logs      # 查看日志
make dev-test      # 运行测试

# 一键启动
make quick         # 快速启动测试
```

## 常用开发流程

### 🔄 日常开发流程

1. **启动开发环境**
   ```bash
   ./quick.sh
   ```

2. **修改代码后重启服务**
   ```bash
   ./dev.sh restart
   ```

3. **查看实时日志**
   ```bash
   ./dev.sh logs
   ```

4. **运行API测试**
   ```bash
   ./dev.sh test
   ```

### 🐛 调试流程

1. **前台运行查看详细日志**
   ```bash
   ./dev.sh dev
   ```
   
2. **在另一个终端运行测试**
   ```bash
   ./dev.sh test
   ```

3. **检查服务状态**
   ```bash
   ./dev.sh status
   ./dev.sh health
   ```

### 🧪 测试流程

1. **确保服务运行**
   ```bash
   ./dev.sh status
   ```

2. **运行完整测试**
   ```bash
   ./dev.sh test
   ```

3. **手动测试特定接口**
   ```bash
   # 健康检查
   curl http://localhost:8080/health
   
   # 发送验证码
   curl -X POST http://localhost:8080/api/v1/user/send-code \
     -H "Content-Type: application/json" \
     -d '{"phone":"13800138000"}'
   ```

## 服务信息

### 📡 服务地址

- **API Base URL**: `http://localhost:8080`
- **健康检查**: `http://localhost:8080/health`

### 📁 重要文件

| 文件 | 用途 |
|------|------|
| `solve_api.pid` | 进程PID文件 |
| `logs/dev.log` | 开发日志文件 |
| `logs/app.log` | 应用日志文件 |
| `config/config.yaml` | 配置文件 |

### 🔧 配置说明

服务使用以下配置：
- **端口**: 8080
- **数据库**: 远程MySQL (***********)
- **缓存**: 远程Redis (************)
- **短信**: 阿里云SMS服务

## 故障排查

### ❌ 常见问题

1. **服务启动失败**
   ```bash
   # 检查端口占用
   lsof -i :8080
   
   # 查看详细错误
   ./dev.sh dev
   ```

2. **数据库连接失败**
   ```bash
   # 检查网络连接
   ping ***********
   
   # 检查配置文件
   cat config/config.yaml
   ```

3. **Redis连接失败**
   ```bash
   # 检查Redis连接
   ping ************
   ```

4. **API测试失败**
   ```bash
   # 检查服务状态
   ./dev.sh health
   
   # 查看日志
   ./dev.sh logs
   ```

### 🔍 调试技巧

1. **查看详细日志**
   ```bash
   # 实时日志
   ./dev.sh logs
   
   # 历史日志
   cat logs/dev.log
   ```

2. **检查进程状态**
   ```bash
   ./dev.sh status
   ```

3. **手动测试接口**
   ```bash
   # 使用curl测试
   curl -v http://localhost:8080/health
   ```

4. **重置环境**
   ```bash
   ./dev.sh clean
   ./dev.sh start
   ```

## 开发建议

### 💡 最佳实践

1. **开发时使用前台模式**
   ```bash
   ./dev.sh dev
   ```
   可以看到实时日志，方便调试

2. **修改代码后及时重启**
   ```bash
   ./dev.sh restart
   ```

3. **定期运行测试**
   ```bash
   ./dev.sh test
   ```

4. **提交代码前清理环境**
   ```bash
   ./dev.sh clean
   ```

### 🎯 效率提升

1. **使用别名**
   ```bash
   # 添加到 ~/.bashrc 或 ~/.zshrc
   alias ds='./dev.sh'
   alias dstart='./dev.sh start'
   alias dstop='./dev.sh stop'
   alias dtest='./dev.sh test'
   alias dlogs='./dev.sh logs'
   ```

2. **多终端开发**
   - 终端1: 运行服务 `./dev.sh dev`
   - 终端2: 运行测试 `./dev.sh test`
   - 终端3: 查看日志 `./dev.sh logs`

3. **快速重启测试**
   ```bash
   ./dev.sh restart && ./dev.sh test
   ```

## 脚本特性

### ✨ 主要特性

- 🎨 **彩色输出**: 不同类型信息用不同颜色显示
- 🔄 **智能检测**: 自动检测服务运行状态
- 🛡️ **安全停止**: 优雅停止服务，避免数据丢失
- 📊 **状态监控**: 显示进程信息和资源使用
- 🧹 **自动清理**: 清理临时文件和日志
- ⚡ **快速启动**: 一键启动和测试

### 🔧 技术细节

- 使用PID文件管理进程
- 支持前台和后台运行模式
- 自动健康检查
- 智能依赖检测
- 优雅的错误处理

---

**提示**: 如果遇到问题，可以查看 `./dev.sh help` 获取完整命令列表，或查看日志文件进行调试。
