# 密码管理 - 前端开发指南

## 🎯 功能概述

项目已完整实现了**用户和管理员的密码管理功能**，包括使用原密码修改和短信验证码找回密码两种方式。

## 📋 已实现功能

### ✅ 用户密码管理
- **修改密码**: `PUT /api/v1/user/{user_id}/change-password` - 使用原密码修改
- **忘记密码**: `POST /api/v1/user/forgot-password` - 发送验证码到手机
- **重置密码**: `POST /api/v1/user/reset-password` - 使用验证码重置

### ✅ 管理员密码管理
- **管理员登录**: `POST /api/v1/admin/login` - 用户名密码登录
- **修改密码**: `PUT /api/v1/admin/{admin_id}/change-password` - 使用原密码修改
- **忘记密码**: `POST /api/v1/admin/forgot-password` - 发送验证码
- **重置密码**: `POST /api/v1/admin/reset-password` - 使用验证码重置
- **重置用户密码**: `PUT /api/v1/admin/{admin_id}/user/{user_id}/reset-password` - 管理员重置用户密码

## 🔧 技术规范

### 密码要求
- **长度**: 6-20个字符
- **字符**: 只能包含字母和数字
- **加密**: 后端使用bcrypt算法

### API调用
```javascript
// 用户修改密码（使用原密码）
const changePassword = async (userId, oldPassword, newPassword) => {
  const response = await fetch(`/api/v1/user/${userId}/change-password`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      old_password: oldPassword,
      new_password: newPassword
    })
  });
  return response.json();
};

// 用户发送忘记密码验证码
const sendForgotPasswordCode = async (phone) => {
  const response = await fetch('/api/v1/user/forgot-password', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ phone })
  });
  return response.json();
};

// 用户重置密码（使用验证码）
const resetPassword = async (phone, code, newPassword) => {
  const response = await fetch('/api/v1/user/reset-password', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      phone,
      code,
      new_password: newPassword
    })
  });
  return response.json();
};

// 管理员登录
const adminLogin = async (username, password) => {
  const response = await fetch('/api/v1/admin/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, password })
  });
  return response.json();
};
```

## 🎨 页面设计建议

### 1. 用户修改密码页面布局
```
┌─────────────────────────────────────┐
│ 修改密码                            │
├─────────────────────────────────────┤
│ 原密码:                             │
│ [____________________] 👁           │
│ 请输入当前密码                      │
├─────────────────────────────────────┤
│ 新密码:                             │
│ [____________________] 👁           │
│ 6-20位字符，包含字母和数字          │
├─────────────────────────────────────┤
│ 确认密码:                           │
│ [____________________] 👁           │
│ 请再次输入新密码                    │
├─────────────────────────────────────┤
│ 密码强度: ████████░░ 强             │
├─────────────────────────────────────┤
│           [取消] [确认修改]         │
└─────────────────────────────────────┘
```

### 2. 忘记密码页面布局
```
┌─────────────────────────────────────┐
│ 忘记密码                            │
├─────────────────────────────────────┤
│ 手机号:                             │
│ [____________________]              │
│ 请输入注册时的手机号                │
├─────────────────────────────────────┤
│ 验证码:                             │
│ [____________] [发送验证码(60s)]     │
│ 请输入手机收到的验证码              │
├─────────────────────────────────────┤
│ 新密码:                             │
│ [____________________] 👁           │
│ 6-20位字符，包含字母和数字          │
├─────────────────────────────────────┤
│ 确认密码:                           │
│ [____________________] 👁           │
│ 请再次输入新密码                    │
├─────────────────────────────────────┤
│           [返回登录] [重置密码]     │
└─────────────────────────────────────┘
```

### 3. 管理员登录页面布局
```
┌─────────────────────────────────────┐
│ 管理员登录                          │
├─────────────────────────────────────┤
│ 用户名:                             │
│ [____________________]              │
│ 请输入管理员用户名                  │
├─────────────────────────────────────┤
│ 密码:                               │
│ [____________________] 👁           │
│ 请输入管理员密码                    │
├─────────────────────────────────────┤
│           [忘记密码] [登录]         │
└─────────────────────────────────────┘
```

### 2. 表单验证提示
- ✅ **成功**: 绿色提示 "密码修改成功"
- ❌ **错误**: 红色提示具体错误信息
- ⚠️ **警告**: 黄色提示格式要求

### 3. 交互设计
- **实时验证**: 输入时显示密码强度
- **显示/隐藏**: 密码可见性切换
- **确认对话框**: 修改前二次确认

## 💻 代码实现

### 1. Vue.js 组件示例

```vue
<template>
  <div class="password-change-form">
    <h3>修改密码</h3>
    
    <!-- 新密码输入 -->
    <div class="form-item">
      <label>新密码</label>
      <div class="password-input">
        <input 
          :type="showPassword ? 'text' : 'password'"
          v-model="newPassword"
          placeholder="6-20位字符，包含字母和数字"
          @input="validatePassword"
        />
        <button @click="showPassword = !showPassword">
          {{ showPassword ? '隐藏' : '显示' }}
        </button>
      </div>
      <div class="password-strength">
        密码强度: <span :class="strengthClass">{{ strengthText }}</span>
      </div>
      <span v-if="passwordError" class="error">{{ passwordError }}</span>
    </div>
    
    <!-- 确认密码输入 -->
    <div class="form-item">
      <label>确认密码</label>
      <input 
        type="password"
        v-model="confirmPassword"
        placeholder="请再次输入新密码"
        @input="validateConfirm"
      />
      <span v-if="confirmError" class="error">{{ confirmError }}</span>
    </div>
    
    <!-- 操作按钮 -->
    <div class="form-actions">
      <button @click="cancel">取消</button>
      <button 
        @click="submitChange" 
        :disabled="!canSubmit || loading"
        class="primary"
      >
        {{ loading ? '修改中...' : '确认修改' }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  props: ['userId'],
  
  data() {
    return {
      newPassword: '',
      confirmPassword: '',
      showPassword: false,
      loading: false,
      passwordError: '',
      confirmError: ''
    }
  },
  
  computed: {
    // 密码强度计算
    passwordStrength() {
      const password = this.newPassword;
      if (!password) return 0;
      
      let strength = 0;
      if (password.length >= 6) strength += 25;
      if (password.length >= 10) strength += 25;
      if (/[a-z]/.test(password)) strength += 25;
      if (/[A-Z]/.test(password)) strength += 25;
      
      return strength;
    },
    
    strengthClass() {
      if (this.passwordStrength < 50) return 'weak';
      if (this.passwordStrength < 75) return 'medium';
      return 'strong';
    },
    
    strengthText() {
      if (this.passwordStrength < 50) return '弱';
      if (this.passwordStrength < 75) return '中';
      return '强';
    },
    
    canSubmit() {
      return this.newPassword && 
             this.confirmPassword && 
             !this.passwordError && 
             !this.confirmError;
    }
  },
  
  methods: {
    validatePassword() {
      this.passwordError = '';
      
      if (!this.newPassword) return;
      
      if (this.newPassword.length < 6 || this.newPassword.length > 20) {
        this.passwordError = '密码长度必须为6-20个字符';
        return;
      }
      
      if (!/^[a-zA-Z0-9]+$/.test(this.newPassword)) {
        this.passwordError = '密码只能包含字母和数字';
        return;
      }
    },
    
    validateConfirm() {
      this.confirmError = '';
      
      if (this.confirmPassword && this.newPassword !== this.confirmPassword) {
        this.confirmError = '两次输入的密码不一致';
      }
    },
    
    async submitChange() {
      if (!this.canSubmit) return;
      
      // 最终确认
      if (!confirm('确定要修改密码吗？修改后建议重新登录。')) {
        return;
      }
      
      this.loading = true;
      
      try {
        const response = await fetch(`/api/v1/user/profile/${this.userId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ password: this.newPassword })
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
          this.$message.success('密码修改成功，建议重新登录');
          this.resetForm();
          this.$emit('success');
        } else {
          this.$message.error(result.message || '修改失败');
        }
      } catch (error) {
        this.$message.error('网络错误，请重试');
      } finally {
        this.loading = false;
      }
    },
    
    cancel() {
      this.resetForm();
      this.$emit('cancel');
    },
    
    resetForm() {
      this.newPassword = '';
      this.confirmPassword = '';
      this.passwordError = '';
      this.confirmError = '';
      this.showPassword = false;
    }
  }
}
</script>

<style scoped>
.password-change-form {
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
}

.form-item {
  margin-bottom: 20px;
}

.password-input {
  display: flex;
  align-items: center;
}

.password-input input {
  flex: 1;
  margin-right: 10px;
}

.password-strength {
  margin-top: 5px;
  font-size: 12px;
}

.weak { color: #ff4d4f; }
.medium { color: #faad14; }
.strong { color: #52c41a; }

.error {
  color: #ff4d4f;
  font-size: 12px;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

button.primary {
  background: #1890ff;
  color: white;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
```

### 2. React 组件示例

```jsx
import React, { useState, useCallback } from 'react';

const PasswordChangeForm = ({ userId, onSuccess, onCancel }) => {
  const [formData, setFormData] = useState({
    newPassword: '',
    confirmPassword: ''
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // 密码验证
  const validatePassword = useCallback((password) => {
    const errors = [];
    
    if (password.length < 6 || password.length > 20) {
      errors.push('密码长度必须为6-20个字符');
    }
    
    if (!/^[a-zA-Z0-9]+$/.test(password)) {
      errors.push('密码只能包含字母和数字');
    }
    
    return errors;
  }, []);

  // 处理输入变化
  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 实时验证
    if (field === 'newPassword') {
      const passwordErrors = validatePassword(value);
      setErrors(prev => ({ 
        ...prev, 
        newPassword: passwordErrors.length > 0 ? passwordErrors[0] : '' 
      }));
    }
    
    if (field === 'confirmPassword') {
      setErrors(prev => ({
        ...prev,
        confirmPassword: value !== formData.newPassword ? '两次输入的密码不一致' : ''
      }));
    }
  };

  // 提交修改
  const handleSubmit = async () => {
    if (!formData.newPassword || !formData.confirmPassword || 
        errors.newPassword || errors.confirmPassword) {
      return;
    }

    if (!window.confirm('确定要修改密码吗？修改后建议重新登录。')) {
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`/api/v1/user/profile/${userId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: formData.newPassword })
      });

      const result = await response.json();

      if (result.code === 200) {
        alert('密码修改成功，建议重新登录');
        onSuccess?.();
      } else {
        alert(result.message || '修改失败');
      }
    } catch (error) {
      alert('网络错误，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="password-change-form">
      <h3>修改密码</h3>
      
      <div className="form-item">
        <label>新密码</label>
        <div className="password-input">
          <input
            type={showPassword ? 'text' : 'password'}
            value={formData.newPassword}
            onChange={(e) => handleInputChange('newPassword', e.target.value)}
            placeholder="6-20位字符，包含字母和数字"
          />
          <button onClick={() => setShowPassword(!showPassword)}>
            {showPassword ? '隐藏' : '显示'}
          </button>
        </div>
        {errors.newPassword && <span className="error">{errors.newPassword}</span>}
      </div>

      <div className="form-item">
        <label>确认密码</label>
        <input
          type="password"
          value={formData.confirmPassword}
          onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
          placeholder="请再次输入新密码"
        />
        {errors.confirmPassword && <span className="error">{errors.confirmPassword}</span>}
      </div>

      <div className="form-actions">
        <button onClick={onCancel}>取消</button>
        <button 
          onClick={handleSubmit}
          disabled={loading || errors.newPassword || errors.confirmPassword}
          className="primary"
        >
          {loading ? '修改中...' : '确认修改'}
        </button>
      </div>
    </div>
  );
};

export default PasswordChangeForm;
```

## ⚠️ 重要提醒

### 当前限制
1. **只有用户修改密码功能** - 管理员功能未实现
2. **无找回密码功能** - 需要后续开发
3. **密码策略简单** - 只支持字母数字组合

### 安全建议
1. **HTTPS传输** - 生产环境必须使用HTTPS
2. **会话管理** - 密码修改后建议重新登录
3. **操作确认** - 重要操作需要用户确认
4. **错误处理** - 友好的错误提示信息

### 待开发功能
1. **管理员登录** - 管理员Web界面
2. **管理员重置用户密码** - 后台管理功能
3. **找回密码** - 短信验证码重置
4. **密码策略增强** - 支持特殊字符等
