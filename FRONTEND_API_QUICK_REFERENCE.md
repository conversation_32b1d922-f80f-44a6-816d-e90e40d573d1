# 密码管理API快速参考 - 前端开发

## 🚀 快速开始

**Base URL**: `http://localhost:8080`  
**Content-Type**: `application/json`

---

## 📋 API接口列表

### 👤 用户密码管理

| 功能 | 方法 | 接口地址 | 说明 |
|------|------|----------|------|
| 修改密码 | PUT | `/api/v1/user/{user_id}/change-password` | 使用原密码修改 |
| 发送验证码 | POST | `/api/v1/user/forgot-password` | 忘记密码发送验证码 |
| 重置密码 | POST | `/api/v1/user/reset-password` | 使用验证码重置密码 |

### 👨‍💼 管理员密码管理

| 功能 | 方法 | 接口地址 | 说明 |
|------|------|----------|------|
| 管理员登录 | POST | `/api/v1/admin/login` | 用户名密码登录 |
| 修改密码 | PUT | `/api/v1/admin/{admin_id}/change-password` | 使用原密码修改 |
| 发送验证码 | POST | `/api/v1/admin/forgot-password` | 忘记密码发送验证码 |
| 重置密码 | POST | `/api/v1/admin/reset-password` | 使用验证码重置密码 |
| 重置用户密码 | PUT | `/api/v1/admin/{admin_id}/user/{user_id}/reset-password` | 管理员重置用户密码 |

---

## 📝 请求参数

### 用户修改密码
```json
PUT /api/v1/user/{user_id}/change-password
{
  "old_password": "oldpass123",
  "new_password": "newpass123"
}
```

### 发送验证码（用户）
```json
POST /api/v1/user/forgot-password
{
  "phone": "13800138000"
}
```

### 重置密码（用户）
```json
POST /api/v1/user/reset-password
{
  "phone": "13800138000",
  "code": "123456",
  "new_password": "newpass123"
}
```

### 管理员登录
```json
POST /api/v1/admin/login
{
  "username": "admin",
  "password": "admin123"
}
```

### 管理员修改密码
```json
PUT /api/v1/admin/{admin_id}/change-password
{
  "old_password": "oldpass123",
  "new_password": "newpass123"
}
```

### 发送验证码（管理员）
```json
POST /api/v1/admin/forgot-password
{
  "username": "admin"
}
```

### 重置密码（管理员）
```json
POST /api/v1/admin/reset-password
{
  "username": "admin",
  "code": "123456",
  "new_password": "newpass123"
}
```

### 管理员重置用户密码
```json
PUT /api/v1/admin/{admin_id}/user/{user_id}/reset-password
{
  "new_password": "newpass123"
}
```

---

## ✅ 成功响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 用户或管理员信息
  }
}
```

---

## ❌ 错误码说明

| 错误码 | 说明 | 常见原因 |
|--------|------|----------|
| 400 | 请求参数错误 | 参数格式不正确、必填参数缺失 |
| 401 | 认证失败 | 密码错误、验证码错误或过期 |
| 403 | 权限不足 | 账户被冻结、权限不够 |
| 404 | 资源不存在 | 用户不存在、手机号未注册 |
| 429 | 请求过于频繁 | 验证码发送间隔不足60秒 |
| 500 | 服务器错误 | 系统内部错误 |

---

## 🔧 技术要求

### 密码规则
- **长度**: 6-20位
- **字符**: 必须包含字母和数字
- **限制**: 不支持特殊字符

### 验证码规则
- **格式**: 6位数字
- **有效期**: 5分钟
- **发送限制**: 60秒内只能发送一次

### 权限说明
- **用户**: 只能修改自己的密码
- **普通管理员**: 只能修改自己的密码
- **超级管理员**: 可以重置用户密码

---

## 🧪 测试数据

### 默认管理员
- **用户名**: `15688515913`
- **密码**: `admin888`
- **角色**: 超级管理员

### 测试用户
- **手机号**: `15653259315`
- **密码**: `123456`

---

## 💻 JavaScript示例

### 基础API调用
```javascript
// 通用API调用函数
async function callAPI(url, method, data = null) {
  try {
    const options = {
      method,
      headers: { 'Content-Type': 'application/json' }
    };
    
    if (data) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(url, options);
    const result = await response.json();
    
    return result;
  } catch (error) {
    throw new Error('网络错误');
  }
}

// 用户修改密码
async function changeUserPassword(userId, oldPassword, newPassword) {
  return await callAPI(
    `/api/v1/user/${userId}/change-password`,
    'PUT',
    { old_password: oldPassword, new_password: newPassword }
  );
}

// 发送验证码
async function sendVerificationCode(phone) {
  return await callAPI(
    '/api/v1/user/forgot-password',
    'POST',
    { phone }
  );
}

// 重置密码
async function resetPassword(phone, code, newPassword) {
  return await callAPI(
    '/api/v1/user/reset-password',
    'POST',
    { phone, code, new_password: newPassword }
  );
}

// 管理员登录
async function adminLogin(username, password) {
  return await callAPI(
    '/api/v1/admin/login',
    'POST',
    { username, password }
  );
}
```

### 验证码倒计时
```javascript
function startCountdown(buttonId, seconds = 60) {
  const button = document.getElementById(buttonId);
  let count = seconds;
  
  button.disabled = true;
  
  const timer = setInterval(() => {
    if (count <= 0) {
      clearInterval(timer);
      button.disabled = false;
      button.textContent = '发送验证码';
    } else {
      button.textContent = `${count}秒后重试`;
      count--;
    }
  }, 1000);
}
```

### 密码强度验证
```javascript
function validatePassword(password) {
  const errors = [];
  
  if (password.length < 6 || password.length > 20) {
    errors.push('密码长度必须为6-20位');
  }
  
  if (!/[a-zA-Z]/.test(password)) {
    errors.push('密码必须包含字母');
  }
  
  if (!/\d/.test(password)) {
    errors.push('密码必须包含数字');
  }
  
  return {
    valid: errors.length === 0,
    errors: errors
  };
}
```

### 错误处理
```javascript
function handleError(result) {
  const errorMessages = {
    400: '请求参数错误，请检查输入',
    401: '认证失败，请检查密码或验证码',
    403: '权限不足或账户被冻结',
    404: '用户不存在或手机号未注册',
    429: '操作过于频繁，请稍后再试',
    500: '服务器错误，请稍后重试'
  };
  
  return errorMessages[result.code] || result.message || '未知错误';
}
```

---

## 📞 支持

如有问题请联系后端开发团队。

**文档版本**: v1.0  
**更新时间**: 2024-06-07
