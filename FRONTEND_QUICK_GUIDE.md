# 应用管理模块 - 前端开发快速指南

## 🚀 快速开始

### 基础信息
- **API Base URL**: `http://localhost:8080`
- **模块功能**: 用户应用创建和管理
- **核心概念**: 每个应用有唯一的 `app_key` 和 `secret_key`

## 📋 核心业务规则

| 规则 | 说明 |
|------|------|
| 应用数量限制 | 每用户最多5个应用 |
| 密钥唯一性 | app_key全局唯一（32位） |
| 密钥安全性 | secret_key可重置（64位） |
| 权限隔离 | 用户只能操作自己的应用 |
| 状态控制 | 冻结应用无法调用API |

## 🎯 主要页面设计

### 1. 应用列表页面
```
┌─────────────────────────────────────┐
│ 我的应用                [+ 创建应用] │
├─────────────────────────────────────┤
│ 📱 我的搜题应用          [正常]     │
│    App Key: abcd1234...             │
│    创建时间: 2024-01-01             │
│    [详情] [编辑] [冻结]             │
├─────────────────────────────────────┤
│ 📱 测试应用              [冻结]     │
│    App Key: efgh5678...             │
│    创建时间: 2024-01-02             │
│    [详情] [编辑] [恢复]             │
└─────────────────────────────────────┘
```

### 2. 应用详情页面
```
┌─────────────────────────────────────┐
│ 应用详情                    [编辑]  │
├─────────────────────────────────────┤
│ 应用名称: 我的搜题应用              │
│ 应用类型: 拍照搜题                  │
│ 应用状态: [正常] [切换为冻结]       │
├─────────────────────────────────────┤
│ App Key:                            │
│ abcd1234efgh5678ijkl9012mnop3456    │
│                            [复制]   │
├─────────────────────────────────────┤
│ Secret Key:                         │
│ ********************************    │
│ [显示] [复制] [重置密钥]            │
├─────────────────────────────────────┤
│ 创建时间: 2024-01-01 12:00:00       │
│ 更新时间: 2024-01-01 12:00:00       │
└─────────────────────────────────────┘
```

### 3. 创建应用页面
```
┌─────────────────────────────────────┐
│ 创建应用                            │
├─────────────────────────────────────┤
│ 应用名称: [________________]        │
│          (1-50个字符)               │
├─────────────────────────────────────┤
│ 业务类型: [拍照搜题 ▼]              │
├─────────────────────────────────────┤
│           [取消] [创建应用]         │
└─────────────────────────────────────┘
```

## 🔌 API 接口速查

### 基础CRUD操作

| 操作 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 创建 | POST | `/api/v1/user/{user_id}/app` | 创建新应用 |
| 列表 | GET | `/api/v1/user/{user_id}/app` | 获取应用列表 |
| 详情 | GET | `/api/v1/user/{user_id}/app/{app_id}` | 获取应用详情 |
| 更新 | PUT | `/api/v1/user/{user_id}/app/{app_id}` | 更新应用名称 |

### 特殊操作

| 操作 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 重置密钥 | PUT | `/api/v1/user/{user_id}/app/{app_id}/reset-secret` | 重置SecretKey |
| 状态管理 | PUT | `/api/v1/user/{user_id}/app/{app_id}/status` | 冻结/恢复应用 |

## 💻 代码示例

### 1. 获取应用列表
```javascript
async function getApplications(userId) {
  try {
    const response = await fetch(`/api/v1/user/${userId}/app`);
    const result = await response.json();
    
    if (result.code === 200) {
      return result.data; // 应用列表数组
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取应用列表失败:', error);
    throw error;
  }
}
```

### 2. 创建应用
```javascript
async function createApplication(userId, name, type = 1) {
  try {
    const response = await fetch(`/api/v1/user/${userId}/app`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name, type })
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      // 创建成功，返回包含密钥的完整信息
      return result.data;
    } else if (result.code === 409) {
      throw new Error('应用数量已达上限（5个）');
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('创建应用失败:', error);
    throw error;
  }
}
```

### 3. 重置密钥
```javascript
async function resetSecretKey(userId, appId) {
  // 先确认用户操作
  if (!confirm('重置密钥后，旧密钥将立即失效。确定要继续吗？')) {
    return;
  }
  
  try {
    const response = await fetch(`/api/v1/user/${userId}/app/${appId}/reset-secret`, {
      method: 'PUT'
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      // 返回包含新密钥的应用信息
      alert('密钥重置成功！请及时更新客户端配置。');
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('重置密钥失败:', error);
    throw error;
  }
}
```

## 🎨 UI/UX 建议

### 状态显示
```css
/* 应用状态样式 */
.status-normal {
  background: #52c41a;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
}

.status-frozen {
  background: #ff4d4f;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
}
```

### 密钥显示
- 默认隐藏SecretKey，显示为星号
- 提供"显示/隐藏"切换按钮
- 提供一键复制功能
- 重置密钥需要二次确认

### 交互反馈
- 创建成功后显示密钥信息弹窗
- 操作按钮添加loading状态
- 错误信息友好提示
- 成功操作给予明确反馈

## ⚠️ 重要提醒

### 安全注意事项
1. **密钥保护**: SecretKey只在创建和重置时显示，请提醒用户妥善保存
2. **操作确认**: 重置密钥、冻结应用等操作需要用户确认
3. **权限验证**: 确保用户只能操作自己的应用

### 错误处理
```javascript
// 统一错误处理
function handleApiError(error, result) {
  switch (result?.code) {
    case 400:
      return '请求参数错误，请检查输入信息';
    case 403:
      return '账户已被冻结，请联系管理员';
    case 404:
      return '应用不存在或已被删除';
    case 409:
      return '应用数量已达上限（5个），请删除不需要的应用';
    case 500:
      return '服务器错误，请稍后重试';
    default:
      return result?.message || '操作失败，请重试';
  }
}
```

### 数据验证
```javascript
// 前端验证规则
const validation = {
  appName: {
    required: true,
    minLength: 1,
    maxLength: 50,
    message: '应用名称为1-50个字符'
  },
  appType: {
    required: true,
    enum: [1],
    message: '请选择有效的应用类型'
  }
};
```

## 📱 响应式设计

### 移动端适配
- 应用卡片采用垂直布局
- 操作按钮适当增大点击区域
- 密钥显示考虑横向滚动
- 确认对话框适配小屏幕

### 桌面端优化
- 应用列表采用表格或卡片网格布局
- 支持批量操作
- 提供搜索和筛选功能
- 密钥显示支持快速复制

## 🔄 状态管理建议

```javascript
// Vue 3 + Pinia 示例
export const useApplicationStore = defineStore('application', {
  state: () => ({
    applications: [],
    loading: false,
    error: null
  }),
  
  actions: {
    async fetchApplications(userId) {
      this.loading = true;
      try {
        this.applications = await getApplications(userId);
      } catch (error) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    }
  }
});
```

这个快速指南为前端开发团队提供了完整的应用管理模块开发指导，包括UI设计、API调用、错误处理和最佳实践建议。
