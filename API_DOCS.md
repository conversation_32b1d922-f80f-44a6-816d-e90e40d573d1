# 拍照搜题 API 接口文档

## 概述

本文档描述了拍照搜题API服务的接口规范，当前版本实现了用户注册与账户模块、应用管理模块的功能。

### 基础信息

- **Base URL**: `http://localhost:8080`
- **API版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8

### 统一响应格式

所有API接口都使用统一的响应格式：

```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

#### 响应状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |
| 503 | 服务不可用 |

## 用户管理接口

### 1. 用户注册

**接口地址**: `POST /api/v1/user/register`

**接口描述**: 通过手机号、验证码、密码和邀请码完成用户注册

**请求参数**:

```json
{
  "phone": "13800138000",
  "password": "123456",
  "code": "123456",
  "invite_code": "SOLVE2024"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| phone | string | 是 | 手机号，11位数字 |
| password | string | 是 | 密码，6-20位字符 |
| code | string | 是 | 验证码，6位数字 |
| invite_code | string | 是 | 邀请码 |

**响应示例**:

```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 0,
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:

- 400: 请求参数错误
- 409: 手机号已注册
- 500: 服务器内部错误

### 2. 用户登录

**接口地址**: `POST /api/v1/user/login`

**接口描述**: 通过手机号和密码进行用户登录

**请求参数**:

```json
{
  "phone": "13800138000",
  "password": "123456"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| phone | string | 是 | 手机号，11位数字 |
| password | string | 是 | 密码 |

**响应示例**:

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 0,
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:

- 400: 请求参数错误
- 401: 用户名或密码错误
- 403: 账户已被冻结
- 500: 服务器内部错误

### 3. 获取用户信息

**接口地址**: `GET /api/v1/user/profile/{user_id}`

**接口描述**: 获取指定用户的详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 0,
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:

- 400: 用户ID格式错误
- 404: 用户不存在
- 500: 服务器内部错误

### 4. 更新用户信息

**接口地址**: `PUT /api/v1/user/profile/{user_id}`

**接口描述**: 更新指定用户的信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID |

**请求参数**:

```json
{
  "password": "newpassword"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| password | string | 否 | 新密码，6-20位字符 |

**响应示例**:

```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 0,
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:

- 400: 请求参数错误
- 404: 用户不存在
- 500: 服务器内部错误

### 5. 发送验证码

**接口地址**: `POST /api/v1/user/send-code`

**接口描述**: 向指定手机号发送验证码

**请求参数**:

```json
{
  "phone": "13800138000"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| phone | string | 是 | 手机号，11位数字 |

**响应示例**:

```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": null
}
```

**错误响应**:

- 400: 请求参数错误
- 429: 发送过于频繁
- 500: 服务器内部错误

## 应用管理接口

### 1. 创建应用

**接口地址**: `POST /api/v1/user/{user_id}/app`

**接口描述**: 用户创建新的应用，自动生成app_key和secret_key

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID |

**请求参数**:

```json
{
  "name": "我的搜题应用",
  "type": 1
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 应用名称，1-50个字符 |
| type | int | 是 | 业务类型，1-拍照搜题 |

**响应示例**:

```json
{
  "code": 200,
  "message": "应用创建成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "我的搜题应用",
    "type": 1,
    "app_key": "abcd1234efgh5678ijkl9012mnop3456",
    "secret_key": "abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz12",
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:

- 400: 请求参数错误
- 403: 账户已被冻结
- 409: 应用数量超限（每个用户最多5个应用）
- 500: 服务器内部错误

### 2. 获取应用列表

**接口地址**: `GET /api/v1/user/{user_id}/app`

**接口描述**: 获取用户的所有应用列表

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "获取应用列表成功",
  "data": [
    {
      "id": 1,
      "name": "我的搜题应用",
      "type": 1,
      "app_key": "abcd1234efgh5678ijkl9012mnop3456",
      "status": 1,
      "created_at": "2024-01-01T12:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z"
    }
  ]
}
```

**错误响应**:

- 400: 请求参数错误
- 404: 用户不存在
- 500: 服务器内部错误

### 3. 获取应用详情

**接口地址**: `GET /api/v1/user/{user_id}/app/{app_id}`

**接口描述**: 获取指定应用的详细信息，包含secret_key

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID |
| app_id | int | 是 | 应用ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "获取应用详情成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "我的搜题应用",
    "type": 1,
    "app_key": "abcd1234efgh5678ijkl9012mnop3456",
    "secret_key": "abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz12",
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:

- 400: 请求参数错误
- 404: 应用不存在
- 500: 服务器内部错误

### 4. 更新应用信息

**接口地址**: `PUT /api/v1/user/{user_id}/app/{app_id}`

**接口描述**: 更新应用名称（不能修改类型）

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID |
| app_id | int | 是 | 应用ID |

**请求参数**:

```json
{
  "name": "新的应用名称"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 新的应用名称，1-50个字符 |

**响应示例**:

```json
{
  "code": 200,
  "message": "应用更新成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "新的应用名称",
    "type": 1,
    "app_key": "abcd1234efgh5678ijkl9012mnop3456",
    "secret_key": "abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz12",
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:

- 400: 请求参数错误
- 403: 账户已被冻结
- 404: 应用不存在
- 500: 服务器内部错误

### 5. 重置SecretKey

**接口地址**: `PUT /api/v1/user/{user_id}/app/{app_id}/reset-secret`

**接口描述**: 重置应用的SecretKey，生成新的密钥

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID |
| app_id | int | 是 | 应用ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "SecretKey重置成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "我的搜题应用",
    "type": 1,
    "app_key": "abcd1234efgh5678ijkl9012mnop3456",
    "secret_key": "newabcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuv",
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:

- 400: 请求参数错误
- 403: 账户已被冻结
- 404: 应用不存在
- 500: 服务器内部错误

### 6. 更新应用状态

**接口地址**: `PUT /api/v1/user/{user_id}/app/{app_id}/status`

**接口描述**: 更新应用状态（正常/冻结）

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID |
| app_id | int | 是 | 应用ID |

**请求参数**:

```json
{
  "status": 2
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | int | 是 | 应用状态，1-正常，2-冻结 |

**响应示例**:

```json
{
  "code": 200,
  "message": "应用状态更新成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "我的搜题应用",
    "type": 1,
    "app_key": "abcd1234efgh5678ijkl9012mnop3456",
    "secret_key": "abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz12",
    "status": 2,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:

- 400: 请求参数错误
- 403: 账户已被冻结
- 404: 应用不存在
- 500: 服务器内部错误

## 系统接口

### 健康检查

**接口地址**: `GET /health`

**接口描述**: 检查服务健康状态

**响应示例**:

```json
{
  "status": "ok",
  "app": "solve_api",
  "version": "1.0.0"
}
```

## 数据模型

### 用户模型

```json
{
  "id": 1,
  "phone": "13800138000",
  "balance": 0,
  "status": 1,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 用户ID |
| phone | string | 手机号 |
| balance | float | 账户余额 |
| status | int | 用户状态：1-正常，2-冻结 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |

### 应用模型

```json
{
  "id": 1,
  "user_id": 1,
  "name": "我的搜题应用",
  "type": 1,
  "app_key": "abcd1234efgh5678ijkl9012mnop3456",
  "secret_key": "abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz12",
  "status": 1,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 应用ID |
| user_id | int | 用户ID |
| name | string | 应用名称 |
| type | int | 业务类型：1-拍照搜题 |
| app_key | string | 应用密钥，32位字符串 |
| secret_key | string | 密钥，64位字符串 |
| status | int | 应用状态：1-正常，2-冻结 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |

## 错误码说明

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| 200 | 200 | 成功 |
| 400 | 400 | 请求参数错误 |
| 401 | 401 | 未授权访问 |
| 403 | 403 | 禁止访问 |
| 404 | 404 | 资源不存在 |
| 409 | 409 | 资源冲突 |
| 429 | 429 | 请求过于频繁 |
| 500 | 500 | 服务器内部错误 |
| 503 | 503 | 服务不可用 |

## 注意事项

1. 所有接口都需要设置正确的Content-Type: application/json
2. 验证码有效期为5分钟
3. 验证码发送频率限制为60秒一次
4. 密码会进行bcrypt加密存储
5. 手机号必须是11位数字且以1开头
6. 邀请码默认为"SOLVE2024"，可通过系统配置修改
7. 每个用户最多可创建5个应用
8. app_key为32位随机字符串，全局唯一
9. secret_key为64位随机字符串，可重置
10. 应用被冻结后无法调用相关API
11. 应用类型创建后不可修改
