# 拍照搜题API服务接口文档

## 概述

本文档描述了拍照搜题API服务的所有接口，包括用户管理、应用管理、拍照搜题、余额管理、统计分析等功能模块。

## 基础信息

- **服务地址**: `http://localhost:8080`
- **API版本**: `v1`
- **数据格式**: `JSON`
- **字符编码**: `UTF-8`

## 认证方式

### 1. 用户接口认证
用户相关接口无需特殊认证，直接调用即可。

### 2. 业务API认证
业务API（如拍照搜题）需要在请求头中携带应用密钥：

```
X-App-Key: your_app_key
X-Secret-Key: your_secret_key
```

### 3. 管理员接口认证
管理员接口需要管理员权限，具体认证方式根据业务需求实现。

## 响应格式

所有接口统一返回以下格式：

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01 12:00:00"
}
```

### 状态码说明

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `402`: 需要付费
- `403`: 禁止访问
- `404`: 资源不存在
- `409`: 资源冲突
- `429`: 请求过于频繁
- `500`: 服务器内部错误

## 接口列表

### 1. 用户管理模块

#### 1.1 发送验证码
- **接口**: `POST /api/v1/user/send-code`
- **描述**: 发送短信验证码
- **请求参数**:
```json
{
  "phone": "15653259315"
}
```

#### 1.2 用户注册
- **接口**: `POST /api/v1/user/register`
- **描述**: 用户注册
- **请求参数**:
```json
{
  "phone": "15653259315",
  "password": "123456",
  "code": "123456",
  "invite_code": "SOLVE2024"
}
```

#### 1.3 用户登录
- **接口**: `POST /api/v1/user/login`
- **描述**: 用户登录
- **请求参数**:
```json
{
  "phone": "15653259315",
  "password": "123456"
}
```

#### 1.4 获取用户资料
- **接口**: `GET /api/v1/user/profile/{user_id}`
- **描述**: 获取用户详细信息

#### 1.5 更新用户资料
- **接口**: `PUT /api/v1/user/profile/{user_id}`
- **描述**: 更新用户信息

#### 1.6 修改密码
- **接口**: `PUT /api/v1/user/{user_id}/change-password`
- **描述**: 修改用户密码

#### 1.7 忘记密码
- **接口**: `POST /api/v1/user/forgot-password`
- **描述**: 发送忘记密码验证码

#### 1.8 重置密码
- **接口**: `POST /api/v1/user/reset-password`
- **描述**: 重置用户密码

### 2. 应用管理模块

#### 2.1 创建应用
- **接口**: `POST /api/v1/user/{user_id}/app`
- **描述**: 创建新应用
- **请求参数**:
```json
{
  "name": "我的搜题应用",
  "type": 1
}
```

#### 2.2 获取用户应用列表
- **接口**: `GET /api/v1/user/{user_id}/app`
- **描述**: 获取用户的所有应用

#### 2.3 更新应用
- **接口**: `PUT /api/v1/user/{user_id}/app/{app_id}`
- **描述**: 更新应用信息

#### 2.4 删除应用
- **接口**: `DELETE /api/v1/user/{user_id}/app/{app_id}`
- **描述**: 删除应用

#### 2.5 重新生成密钥
- **接口**: `PUT /api/v1/user/{user_id}/app/{app_id}/regenerate-keys`
- **描述**: 重新生成应用密钥

### 3. 拍照搜题模块

#### 3.1 拍照搜题
- **接口**: `POST /api/v1/api/search`
- **描述**: 上传图片进行题目识别和解析
- **认证**: 需要API密钥
- **请求参数**:
```json
{
  "image_url": "https://example.com/question.jpg"
}
```
- **响应示例**:
```json
{
  "code": 200,
  "message": "搜题成功",
  "data": {
    "id": 1,
    "content": "求解方程 x^2 + 2x + 1 = 0",
    "analysis": "这是一个一元二次方程...",
    "answer": "x = -1",
    "subject": "数学",
    "grade": "高中",
    "difficulty": 3,
    "source_model": "qwen-vl-plus,deepseek-chat",
    "cache_hit": false,
    "process_time": 1500
  }
}
```

### 4. 余额管理模块

#### 4.1 用户充值
- **接口**: `POST /api/v1/user/{user_id}/recharge`
- **描述**: 为用户账户充值
- **请求参数**:
```json
{
  "amount": 10.00,
  "description": "账户充值"
}
```

#### 4.2 获取用户余额
- **接口**: `GET /api/v1/user/{user_id}/balance`
- **描述**: 获取用户当前余额

#### 4.3 获取余额变动日志
- **接口**: `GET /api/v1/user/{user_id}/balance/logs`
- **描述**: 获取用户余额变动记录
- **查询参数**:
  - `page`: 页码（默认1）
  - `page_size`: 每页数量（默认10）
  - `type`: 日志类型（1:充值 2:消费 3:退款）

#### 4.4 获取余额统计
- **接口**: `GET /api/v1/user/{user_id}/balance/stats`
- **描述**: 获取用户余额统计信息

#### 4.5 检查余额
- **接口**: `GET /api/v1/user/{user_id}/balance/check`
- **描述**: 检查余额是否足够支付指定服务
- **查询参数**:
  - `service_type`: 服务类型

### 5. 管理员模块

#### 5.1 管理员登录
- **接口**: `POST /api/v1/admin/login`
- **描述**: 管理员登录

#### 5.2 模型配置管理
- **接口**: `POST /api/v1/admin/model`
- **描述**: 创建模型配置
- **接口**: `GET /api/v1/admin/model`
- **描述**: 获取模型配置列表
- **接口**: `GET /api/v1/admin/model/{id}`
- **描述**: 获取模型配置详情
- **接口**: `PUT /api/v1/admin/model/{id}`
- **描述**: 更新模型配置
- **接口**: `DELETE /api/v1/admin/model/{id}`
- **描述**: 删除模型配置

#### 5.3 题目管理
- **接口**: `GET /api/v1/admin/question`
- **描述**: 获取题目列表
- **接口**: `GET /api/v1/admin/question/{id}`
- **描述**: 获取题目详情
- **接口**: `GET /api/v1/admin/question/stats`
- **描述**: 获取题目统计信息
- **接口**: `GET /api/v1/admin/question/search`
- **描述**: 搜索题目
- **接口**: `POST /api/v1/admin/question/cache/clear`
- **描述**: 清空题目缓存

#### 5.4 价格配置管理
- **接口**: `POST /api/v1/admin/price`
- **描述**: 创建价格配置
- **接口**: `GET /api/v1/admin/price`
- **描述**: 获取价格配置列表
- **接口**: `PUT /api/v1/admin/price/service/{service_type}/default`
- **描述**: 设置默认价格

#### 5.5 统计分析
- **接口**: `GET /api/v1/admin/stats/system`
- **描述**: 获取系统统计
- **接口**: `GET /api/v1/admin/stats/user/{user_id}`
- **描述**: 获取用户统计
- **接口**: `GET /api/v1/admin/stats/top-users`
- **描述**: 获取排行榜用户

#### 5.6 系统管理
- **接口**: `GET /api/v1/admin/system/info`
- **描述**: 获取系统信息
- **接口**: `GET /api/v1/admin/system/health`
- **描述**: 系统健康检查
- **接口**: `GET /api/v1/admin/system/dashboard`
- **描述**: 获取仪表板数据

## 错误处理

### 常见错误码

- `40001`: 参数验证失败
- `40101`: API密钥无效
- `40201`: 余额不足
- `40301`: 账户被冻结
- `42901`: 请求频率过高
- `50001`: 数据库连接失败
- `50002`: Redis连接失败
- `50003`: 第三方服务调用失败

### 错误响应示例

```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null,
  "timestamp": "2024-01-01 12:00:00"
}
```

## 限流规则

- 用户接口：每分钟100次请求
- 业务API：每分钟根据应用配置限制
- 管理员接口：无限制

## 注意事项

1. 所有时间字段均为北京时间（UTC+8）
2. 金额字段精确到小数点后2位
3. 图片URL必须是可公开访问的有效链接
4. API密钥请妥善保管，避免泄露
5. 建议在生产环境中使用HTTPS协议

## 联系方式

如有问题，请联系技术支持团队。
