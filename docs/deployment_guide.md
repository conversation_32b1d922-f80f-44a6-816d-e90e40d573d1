# 拍照搜题API服务部署指南

## 概述

本文档详细说明了拍照搜题API服务的部署流程，包括环境准备、配置设置、服务启动等步骤。

## 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 20GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+, CentOS 7+) 或 macOS
- **Go语言**: 1.19+
- **MySQL**: 8.0+
- **Redis**: 6.0+

## 环境准备

### 1. 安装Go语言环境

```bash
# 下载并安装Go 1.21
wget https://go.dev/dl/go1.21.0.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz

# 设置环境变量
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
echo 'export GOPATH=$HOME/go' >> ~/.bashrc
source ~/.bashrc

# 验证安装
go version
```

### 2. 安装MySQL

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# CentOS/RHEL
sudo yum install mysql-server

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

### 3. 安装Redis

```bash
# Ubuntu/Debian
sudo apt install redis-server

# CentOS/RHEL
sudo yum install redis

# 启动Redis服务
sudo systemctl start redis
sudo systemctl enable redis
```

## 项目部署

### 1. 获取项目代码

```bash
# 克隆项目（如果使用Git）
git clone <repository_url>
cd solve_api

# 或者直接上传项目文件到服务器
```

### 2. 安装依赖

```bash
# 下载Go模块依赖
go mod download
go mod tidy
```

### 3. 配置文件设置

#### 3.1 创建配置文件

```bash
cp config/config.example.yaml config/config.yaml
```

#### 3.2 编辑配置文件

```yaml
# config/config.yaml
app:
  name: "solve_api"
  port: 8080
  mode: "production"  # development, production
  rate_limit: 10
  cache_ttl: 3600

database:
  host: "localhost"
  port: 3380
  username: "your_username"
  password: "your_password"
  database: "solve_api"
  charset: "utf8mb4"
  max_idle_conns: 10
  max_open_conns: 100

redis:
  host: "r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com"
  port: 6379
  username: "r-bp1t323p6w8yn2cpq0"
  password: "SsYZyxSSr8uEVWKJ"
  db: 0

sms:
  access_key_id: "your_access_key_id"
  access_key_secret: "your_access_key_secret"
  sign_name: "your_sign_name"
  template_code: "your_template_code"

log:
  level: "info"
  file_path: "logs/app.log"
  max_size: 100
  max_backups: 5
  max_age: 30
```

### 4. 数据库初始化

```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE solve_api CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 创建数据库用户（可选）
mysql -u root -p -e "CREATE USER 'solve_api'@'localhost' IDENTIFIED BY 'your_password';"
mysql -u root -p -e "GRANT ALL PRIVILEGES ON solve_api.* TO 'solve_api'@'localhost';"
mysql -u root -p -e "FLUSH PRIVILEGES;"
```

### 5. 编译项目

```bash
# 编译项目
go build -o solve_api cmd/main.go

# 或者使用交叉编译（如果在不同平台编译）
GOOS=linux GOARCH=amd64 go build -o solve_api cmd/main.go
```

### 6. 启动服务

#### 6.1 直接启动

```bash
# 给执行权限
chmod +x solve_api

# 启动服务
./solve_api
```

#### 6.2 使用脚本启动

```bash
# 使用提供的启动脚本
chmod +x scripts/start.sh
./scripts/start.sh start
```

#### 6.3 使用systemd管理（推荐）

创建systemd服务文件：

```bash
sudo vim /etc/systemd/system/solve_api.service
```

服务文件内容：

```ini
[Unit]
Description=Solve API Service
After=network.target mysql.service redis.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/path/to/solve_api
ExecStart=/path/to/solve_api/solve_api
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start solve_api

# 设置开机自启
sudo systemctl enable solve_api

# 查看服务状态
sudo systemctl status solve_api
```

## 反向代理配置（可选）

### 使用Nginx

```bash
# 安装Nginx
sudo apt install nginx  # Ubuntu/Debian
sudo yum install nginx  # CentOS/RHEL
```

创建Nginx配置：

```bash
sudo vim /etc/nginx/sites-available/solve_api
```

配置内容：

```nginx
server {
    listen 80;
    server_name your_domain.com;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 日志配置
    access_log /var/log/nginx/solve_api_access.log;
    error_log /var/log/nginx/solve_api_error.log;
}
```

启用配置：

```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/solve_api /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

## 监控和日志

### 1. 日志管理

```bash
# 查看应用日志
tail -f logs/app.log

# 查看systemd日志
sudo journalctl -u solve_api -f

# 日志轮转配置
sudo vim /etc/logrotate.d/solve_api
```

### 2. 健康检查

```bash
# 检查服务状态
curl http://localhost:8080/health

# 检查系统信息
curl http://localhost:8080/api/v1/admin/system/health
```

## 安全配置

### 1. 防火墙设置

```bash
# 使用ufw（Ubuntu）
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable

# 使用firewalld（CentOS）
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 2. SSL证书配置（推荐）

```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your_domain.com
```

## 性能优化

### 1. 数据库优化

```sql
-- MySQL配置优化
SET GLOBAL innodb_buffer_pool_size = **********;  -- 1GB
SET GLOBAL max_connections = 200;
SET GLOBAL query_cache_size = 67108864;  -- 64MB
```

### 2. Redis优化

```bash
# Redis配置优化
echo 'maxmemory 512mb' >> /etc/redis/redis.conf
echo 'maxmemory-policy allkeys-lru' >> /etc/redis/redis.conf
sudo systemctl restart redis
```

## 备份策略

### 1. 数据库备份

```bash
# 创建备份脚本
cat > /opt/backup_db.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u solve_api -p solve_api > /opt/backups/solve_api_$DATE.sql
find /opt/backups -name "solve_api_*.sql" -mtime +7 -delete
EOF

chmod +x /opt/backup_db.sh

# 添加到crontab
echo "0 2 * * * /opt/backup_db.sh" | crontab -
```

### 2. 应用备份

```bash
# 备份应用文件
tar -czf solve_api_backup_$(date +%Y%m%d).tar.gz solve_api config logs
```

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查配置文件格式
   - 检查端口是否被占用
   - 检查数据库连接

2. **数据库连接失败**
   - 检查MySQL服务状态
   - 验证数据库配置
   - 检查网络连接

3. **Redis连接失败**
   - 检查Redis服务状态
   - 验证Redis配置
   - 检查防火墙设置

### 日志分析

```bash
# 查看错误日志
grep "ERROR" logs/app.log

# 查看最近的日志
tail -100 logs/app.log

# 实时监控日志
tail -f logs/app.log | grep "ERROR\|WARN"
```

## 更新部署

### 1. 停止服务

```bash
sudo systemctl stop solve_api
```

### 2. 备份当前版本

```bash
cp solve_api solve_api.backup
```

### 3. 更新代码

```bash
# 拉取最新代码
git pull origin main

# 重新编译
go build -o solve_api cmd/main.go
```

### 4. 启动服务

```bash
sudo systemctl start solve_api
```

### 5. 验证部署

```bash
curl http://localhost:8080/health
```

## 联系支持

如果在部署过程中遇到问题，请联系技术支持团队。
