# 密码管理API测试命令 - 前端开发

## 📋 概述

本文档提供了完整的curl测试命令，方便前端开发者快速测试所有密码管理API接口。

**Base URL**: `http://localhost:8080`

---

## 🔐 用户密码管理测试

### 1. 用户修改密码（使用原密码）
```bash
curl -X PUT http://localhost:8080/api/v1/user/1/change-password \
  -H "Content-Type: application/json" \
  -d '{
    "old_password": "123456",
    "new_password": "newpass123"
  }'
```

### 2. 发送忘记密码验证码
```bash
curl -X POST http://localhost:8080/api/v1/user/forgot-password \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "15653259315"
  }'
```

### 3. 重置密码（使用验证码）
```bash
curl -X POST http://localhost:8080/api/v1/user/reset-password \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "15653259315",
    "code": "123456",
    "new_password": "resetpass123"
  }'
```

---

## 👨‍💼 管理员密码管理测试

### 4. 管理员登录
```bash
curl -X POST http://localhost:8080/api/v1/admin/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "15688515913",
    "password": "admin888"
  }'
```

### 5. 管理员修改密码（使用原密码）
```bash
curl -X PUT http://localhost:8080/api/v1/admin/1/change-password \
  -H "Content-Type: application/json" \
  -d '{
    "old_password": "admin888",
    "new_password": "newadmin123"
  }'
```

### 6. 管理员发送忘记密码验证码
```bash
curl -X POST http://localhost:8080/api/v1/admin/forgot-password \
  -H "Content-Type: application/json" \
  -d '{
    "username": "15688515913"
  }'
```

### 7. 管理员重置密码（使用验证码）
```bash
curl -X POST http://localhost:8080/api/v1/admin/reset-password \
  -H "Content-Type: application/json" \
  -d '{
    "username": "15688515913",
    "code": "123456",
    "new_password": "resetadmin123"
  }'
```

### 8. 管理员重置用户密码
```bash
curl -X PUT http://localhost:8080/api/v1/admin/1/user/1/reset-password \
  -H "Content-Type: application/json" \
  -d '{
    "new_password": "adminreset123"
  }'
```

---

## ❌ 错误情况测试

### 错误的原密码
```bash
curl -X PUT http://localhost:8080/api/v1/user/1/change-password \
  -H "Content-Type: application/json" \
  -d '{
    "old_password": "wrongpassword",
    "new_password": "newpass123"
  }'
```

### 无效的验证码
```bash
curl -X POST http://localhost:8080/api/v1/user/reset-password \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "15653259315",
    "code": "000000",
    "new_password": "newpass123"
  }'
```

### 不存在的手机号
```bash
curl -X POST http://localhost:8080/api/v1/user/forgot-password \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "19999999999"
  }'
```

### 密码格式错误
```bash
curl -X PUT http://localhost:8080/api/v1/user/1/change-password \
  -H "Content-Type: application/json" \
  -d '{
    "old_password": "123456",
    "new_password": "123"
  }'
```

### 权限不足（普通管理员重置用户密码）
```bash
curl -X PUT http://localhost:8080/api/v1/admin/2/user/1/reset-password \
  -H "Content-Type: application/json" \
  -d '{
    "new_password": "unauthorized123"
  }'
```

---

## 📊 预期响应示例

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "phone": "15653259315",
    "balance": 100.00,
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### 错误响应
```json
{
  "code": 401,
  "message": "原密码错误"
}
```

---

## 🧪 完整测试流程

### 用户密码管理完整流程
```bash
# 1. 用户修改密码
curl -X PUT http://localhost:8080/api/v1/user/1/change-password \
  -H "Content-Type: application/json" \
  -d '{"old_password": "123456", "new_password": "newpass123"}'

# 2. 发送忘记密码验证码
curl -X POST http://localhost:8080/api/v1/user/forgot-password \
  -H "Content-Type: application/json" \
  -d '{"phone": "15653259315"}'

# 3. 使用验证码重置密码（需要真实验证码）
curl -X POST http://localhost:8080/api/v1/user/reset-password \
  -H "Content-Type: application/json" \
  -d '{"phone": "15653259315", "code": "实际收到的验证码", "new_password": "resetpass123"}'
```

### 管理员密码管理完整流程
```bash
# 1. 管理员登录
curl -X POST http://localhost:8080/api/v1/admin/login \
  -H "Content-Type: application/json" \
  -d '{"username": "15688515913", "password": "admin888"}'

# 2. 管理员修改密码
curl -X PUT http://localhost:8080/api/v1/admin/1/change-password \
  -H "Content-Type: application/json" \
  -d '{"old_password": "admin888", "new_password": "newadmin123"}'

# 3. 管理员重置用户密码
curl -X PUT http://localhost:8080/api/v1/admin/1/user/1/reset-password \
  -H "Content-Type: application/json" \
  -d '{"new_password": "adminreset123"}'
```

---

## 🔧 测试脚本

### 自动化测试脚本
```bash
#!/bin/bash

BASE_URL="http://localhost:8080"

echo "=== 密码管理API测试 ==="

# 测试用户修改密码
echo "1. 测试用户修改密码..."
curl -s -X PUT $BASE_URL/api/v1/user/1/change-password \
  -H "Content-Type: application/json" \
  -d '{"old_password": "123456", "new_password": "newpass123"}' | jq

# 测试发送验证码
echo "2. 测试发送验证码..."
curl -s -X POST $BASE_URL/api/v1/user/forgot-password \
  -H "Content-Type: application/json" \
  -d '{"phone": "15653259315"}' | jq

# 测试管理员登录
echo "3. 测试管理员登录..."
curl -s -X POST $BASE_URL/api/v1/admin/login \
  -H "Content-Type: application/json" \
  -d '{"username": "15688515913", "password": "admin888"}' | jq

echo "测试完成！"
```

---

## 📝 注意事项

### 验证码测试
- 真实验证码会发送到手机号 `15653259315`
- 管理员用户名 `15688515913` 也会收到真实短信
- 验证码有效期为5分钟
- 60秒内只能发送一次验证码

### 测试数据
- **测试用户ID**: 1
- **测试管理员ID**: 1
- **测试手机号**: 15653259315
- **管理员用户名**: 15688515913

### 环境要求
- 服务器运行在 `http://localhost:8080`
- 需要安装 `curl` 命令
- 建议安装 `jq` 用于格式化JSON响应

### 密码规则
- 长度：6-20位
- 必须包含字母和数字
- 不支持特殊字符

---

## 📞 支持

如有问题请联系后端开发团队。

**文档版本**: v1.0  
**更新时间**: 2024-06-07
