# 管理员账号信息

## 默认管理员账号

系统已自动创建默认管理员账号，用于系统管理和配置。

### 🔑 账号信息

| 字段 | 值 | 说明 |
|------|-----|------|
| **用户名** | `admin` | 管理员登录用户名 |
| **密码** | `admin123` | 默认密码（建议首次登录后修改） |
| **角色** | `1` | 超级管理员（最高权限） |

### 📋 账号详情

```json
{
  "username": "admin",
  "password": "admin123",
  "role": 1,
  "role_name": "超级管理员"
}
```

### 🛡️ 权限说明

**超级管理员 (role=1)** 拥有以下权限：
- 系统配置管理
- 用户管理
- 应用管理
- 模型配置管理
- 价格配置管理
- 数据统计查看
- 日志查看

**普通管理员 (role=2)** 权限（后续开发）：
- 用户管理
- 应用管理
- 数据统计查看

### 🔐 密码加密

密码使用 **bcrypt** 算法加密存储：
- 原始密码：`admin123`
- 加密后：`$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi`

### 📍 数据库位置

管理员账号存储在 `admins` 表中：

```sql
-- 查看管理员账号
SELECT id, username, role, created_at, updated_at 
FROM admins 
WHERE username = 'admin';
```

### 🔄 账号管理

#### 修改密码

```sql
-- 修改管理员密码（需要先用bcrypt加密新密码）
UPDATE admins 
SET password = '$2a$10$新的bcrypt加密密码', updated_at = NOW() 
WHERE username = 'admin';
```

#### 创建新管理员

```sql
-- 创建普通管理员
INSERT INTO admins (username, password, role, created_at, updated_at) 
VALUES ('manager', '$2a$10$加密后的密码', 2, NOW(), NOW());
```

#### 删除管理员

```sql
-- 软删除管理员
UPDATE admins 
SET deleted_at = NOW() 
WHERE username = 'manager';
```

### 🚀 首次使用建议

1. **立即修改默认密码**
   - 登录后台管理系统
   - 修改默认密码为强密码
   - 建议密码包含大小写字母、数字和特殊字符

2. **创建专用管理员账号**
   - 为不同管理员创建独立账号
   - 根据职责分配不同角色权限
   - 定期审查管理员账号

3. **安全配置**
   - 启用登录日志记录
   - 设置密码复杂度要求
   - 配置登录失败锁定机制

### ⚠️ 安全注意事项

1. **密码安全**
   - 默认密码仅用于初始化，请立即修改
   - 使用强密码策略
   - 定期更换密码

2. **账号管理**
   - 不要在生产环境使用默认账号
   - 为每个管理员创建独立账号
   - 及时删除不再使用的账号

3. **权限控制**
   - 按最小权限原则分配角色
   - 定期审查管理员权限
   - 记录管理员操作日志

### 🔧 开发说明

#### 管理员表结构

```sql
CREATE TABLE `admins` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(100) NOT NULL,
  `role` int NOT NULL COMMENT '角色 1:超级管理员 2:普通管理员',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_admins_username` (`username`),
  KEY `idx_admins_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### 角色定义

```go
const (
    AdminRoleSuperAdmin = 1 // 超级管理员
    AdminRoleAdmin      = 2 // 普通管理员
)
```

### 📝 后续开发

管理员功能将在后续模块中实现：

1. **管理员登录接口**
   - `POST /api/v1/admin/login`
   - JWT Token认证

2. **管理员管理接口**
   - `GET /api/v1/admin/admins` - 获取管理员列表
   - `POST /api/v1/admin/admins` - 创建管理员
   - `PUT /api/v1/admin/admins/:id` - 更新管理员
   - `DELETE /api/v1/admin/admins/:id` - 删除管理员

3. **权限中间件**
   - 管理员身份验证
   - 角色权限检查
   - 操作日志记录

### 📞 技术支持

如需修改管理员账号或遇到相关问题，请联系开发团队。

---

**重要提醒**: 请在生产环境部署前修改默认密码，确保系统安全！
